<?php

namespace App\Http\Controllers\Organizer;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\AdminNotification;
use App\Models\Category;
use App\Models\Event;
use App\Models\GalleryImage;
use App\Models\Location;
use App\Models\Order;
use App\Models\Slot;
use App\Models\Speaker;
use App\Models\TimeSlot;
use App\Models\Transaction;
use App\Rules\FileTypeValidate;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Pagination\LengthAwarePaginator;

class EventController extends Controller
{
        
    // online course
    public function listnewcourseso() 
    {
        $pageTitle = 'List new courses online';

        $courses = DB::table('events')
            ->whereIn('events.type', [6,7])
            ->leftJoin('categories', 'events.category_id', '=', 'categories.id')
            ->leftJoin('locations', 'events.location_id', '=', 'locations.id')
            ->select('events.*', 'categories.name as category_name', 'locations.name as location_name')
            ->orderByDesc('events.id')
            ->paginate(10);

        foreach ($courses as $course) {
            $milestones = DB::table('course_milestones_online')->where('course_id', $course->id)->get();
            $totalSprints = 0;

            foreach ($milestones as $milestone) {
                $sprints = DB::table('course_sprints_online')->where('milestone_id', $milestone->id)->get();
                $totalSprints += count($sprints);

                foreach ($sprints as $sprint) {
                    $sessions = DB::table('course_sessions_online')->where('sprint_id', $sprint->id)->get();
                    $sprint->sessions = $sessions;
                }

                $milestone->sprints = $sprints;
            }

            $course->milestones = $milestones;
            $course->sprint_count = $totalSprints;
        }

        return view('Template::organizer.event.list-new-courseso', compact('pageTitle', 'courses'));
    }

    public function addnewcourseso()
    {
        $pageTitle = 'Add new courses online';
        $categories = [];
        $locations = [];

        // Fetch categories
        try {
            $response = Http::get(env('APP_API') . 'api/collab-caategories');
            if ($response->successful()) {
                $categories = $response->json()['data'];
            } else {
                Log::error('Categories API failed: ' . $response->status());
            }
        } catch (\Exception $e) {
            Log::error('Categories API exception: ' . $e->getMessage());
        }

        // Fetch locations
        try {
            $response = Http::get(env('APP_API') . 'api/collab-locations');
            if ($response->successful()) {
                $locations = $response->json()['data'];
            } else {
                Log::error('Locations API failed: ' . $response->status());
            }
        } catch (\Exception $e) {
            Log::error('Locations API exception: ' . $e->getMessage());
        }
        
            // Initialize empty arrays
            $apartments = [];
            $courses = [];
            $courseMilestones = [];
            $facilities = [];
            $batches = [];
        
            // Fetch apartments
            try {
                $response = Http::get(env('APP_API') . 'api/collab-apartment-name-list');
                if ($response->successful()) {
                    $apartments = $response->json()['data'];
                } else {
                    Log::error('Apartments API failed: ' . $response->status());
                }
            } catch (\Exception $e) {
                Log::error('Apartments API exception: ' . $e->getMessage());
            }
        
            // Fetch courses and milestones
            try {
                $response = Http::get(env('APP_API') . 'api/new-course-list');
                if ($response->successful()) {
                    $courses = $response->json()['data'];
                    foreach ($courses as $course) {
                        $courseMilestones[$course['id']] = $course['milestones'];
                    }
                } else {
                    Log::error('Courses API failed: ' . $response->status());
                }
            } catch (\Exception $e) {
                Log::error('Courses API exception: ' . $e->getMessage());
            }
            $facilities = DB::table('course_facility')->get()->map(function ($item) {
                return (array) $item;
            })->toArray();
             
            $batches = DB::table('batch_master')->select('id','name')->get();
        
            // return view('Template::organizer.event.add-new-courses-assign', compact(
            //     'pageTitle',
            //     'apartments',
            //     'courses',
            //     'batches',
            //     'courseMilestones',
            //     'facilities'
            // ));
        

        return view('Template::organizer.event.add-new-courseso', compact('pageTitle','categories','locations', 'apartments', 'courses', 'batches', 'courseMilestones', 'facilities'));
    }
    
    public function storeo(Request $request)
    {
        $title = $request->input('name');

            // Check for duplicate entry
            $exists = DB::table('events')
                        ->where('title', $title)
                        ->exists();
        
            if ($exists) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Duplicate course online title not allowed.',
                ]);
            }
        
        DB::beginTransaction();

        $results = [
            'event' => false,
           
        ];

        try {
            // Upload course image
            // $courseImagePath = null;
            // if ($request->hasFile('courseImage')) {
            //     $courseImagePath = $request->file('courseImage')->store('courses', 'public');
            // }

            $courseImagePath = null;

            if ($request->hasFile('courseImage')) {
                $courseImagePath = fileUploader(
                    $request->file('courseImage'),
                    getFilePath('eventCover'),
                    getFileSize('eventCover'),
                    $oldImage ?? null,
                );
            }


            // // Parse milestones JSON
            // $milestones = json_decode($request->input('milestones'), true);
            // if (!is_array($milestones)) {
            //     throw new \Exception("Invalid format for milestones");
            // }

            // 1. Insert Event
            $eventId = DB::table('events')->insertGetId([
                'organizer_id' => auth()->guard('organizer')->id(),
                'category_id' => $request->input('category_id'),
                'location_id' => $request->input('location_id'),
                'apartment_id' => $request->input('apartment_id'),
                'link' => $request->input('link'),
                'title' => $request->input('name'),
                'slug' => Str::slug($request->input('name')) . '-' . uniqid(),
                'location_address' => "test address",
                'short_description' => $request->input('shortDescription'),
                'description' => $request->input('description'),
                'learningoutcome' => $request->input('learningoutcome'),
                'age_min' => $request->input('age_min'),
                'age_max' => $request->input('age_max'),
                'start_date' => $request->input('start_date'),
                'end_date' =>$request->input('end_date'),
                'start_time' => $request->input('start_time'),
                'end_time' => $request->input('end_time'),
                'seats' => $request->input('seats'),
                'seats_booked' => 0,
                'price' => $request->input('price'),
                'cover_image' => $courseImagePath,
                'type' => $request->input('course_type'),
                'is_featured' => $request->input('is_featured', 0),
                'step' => 0,
                'status' => 2,
                'online'=> 1,
                'online_type' =>  $request->input('recurring_type'),
                'verification_details' => $request->input('verification_details', ''),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $results['event'] = $eventId ? true : false;

            $milestoneImages = $request->file('milestoneImages', []);
            $sprintImages = $request->file('sprintImages', []); // Nested: [mi][si]
            $milestones = json_decode($request->input('milestones'), true);
            
            // Get course type from request
            $courseType = $request->input('course_type');
            
            if($courseType == 7)
            {
                
                // 2. Insert Milestones, Sprints, Sessions
                foreach ($milestones as $milestoneIndex => $milestoneData) {
    
    
                    $milestoneImagePath = null;
    
                    // Milestone Image
                    if (isset($milestoneImages[$milestoneIndex])) {
                        $milestoneImagePath = fileUploader(
                            $milestoneImages[$milestoneIndex],
                            getFilePath('milestoneCover'),
                            getFileSize('milestoneCover'),
                            null
                        );
                    }
    
                    $milestoneId = DB::table('course_milestones_online')->insertGetId([
                        'course_id' => $eventId,
                        'name' => $milestoneData['name'],
                        'sequence' => $milestoneData['sequence'],
                        'subjects' => json_encode($milestoneData['subjects']),
                        'description' => $milestoneData['description'],
                        'learningOutcome' => $milestoneData['learningOutcome'],
                        'start_date' => $milestoneData['startDate'],
                        'end_date' => $milestoneData['endDate'],
                        'price' => $milestoneData['price'],
                        'reduction_price' => $milestoneData['reductionPrice'],
                        'milestoneImages' => $milestoneImagePath,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
    
                   // $results['milestones'][] = $milestoneId ? "Milestone $milestoneIndex OK" : "Milestone $milestoneIndex Failed";
    
                    foreach ($milestoneData['sprints'] as $sprintIndex => $sprintData) {
                        // $sprintImage = $request->file("milestones.$milestoneIndex.sprints.$sprintIndex.image");
                        // $sprintImagePath = $sprintImage ? $sprintImage->store('sprint_images', 'public') : null;
    
                         $sprintImagePath = null;
    
                        // if ($request->hasFile("milestones.$milestoneIndex.sprints.$sprintIndex.image")) {
                        //     $milestonesprintImage = $request->file("milestones.$milestoneIndex.sprints.$sprintIndex.image");
                        if (isset($sprintImages[$milestoneIndex][$sprintIndex])) {
                            $milestonesprintImage = $sprintImages[$milestoneIndex][$sprintIndex];
    
                            $sprintImagePath = fileUploader(
                                $milestonesprintImage,
                                getFilePath('milestonesprintCover'),  // You should define this path helper in your config if not yet
                                getFileSize('milestonesprintCover'),
                                null // You can handle old image replacement logic here if needed
                            );
                        }
    
                        $sprintId = DB::table('course_sprints_online')->insertGetId([
                            'milestone_id' => $milestoneId,
                            'name' => $sprintData['theme'],
                            'sequence' => $sprintData['sequence'],
                            'no_of_sections' => $sprintData['numberOfSections'] ?? 0,
                            'sprintImages' => $sprintImagePath,
                            'description' => $sprintData['description'],
                            'learningoutcome' => $sprintData['learningOutcome'],
                            'price' => $sprintData['price'],
                            'reduction_price' => $sprintData['reductionPrice'],
                            'slots' => $sprintData['slots'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
    
                       // $results['sprints'][] = $sprintId ? "Sprint $sprintIndex OK" : "Sprint $sprintIndex Failed";
    
                        if (!empty($sprintData['sections'])) {
                            foreach ($sprintData['sections'] as $sessionIndex => $session) {
                                $sessionId = DB::table('course_sessions_online')->insertGetId([
                                    'sprint_id' => $sprintId,
                                    'sequence' => $session['sequence'],
                                    'sessionname' => $session['name'],
                                    'sessionduration' => $session['duration'],
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ]);
                               // $results['sessions'][] = $sessionId ? "Session $sessionIndex OK" : "Session $sessionIndex Failed";
                            }
                        }
                    }
                }
            
            }
            else{
                
                
                if($request->input('recurring_type') ==  '1tomany')
                {
                    // Function to transform sessions grouped by slotname
                    function transformSessionsBySlotname($sessionsInput)
                    {
                        $grouped = [];
                    
                        foreach ($sessionsInput as $session) {
                            $slotname = $session['slotname'] ?? 'No Slotname';
                    
                            if (!isset($grouped[$slotname])) {
                                $grouped[$slotname] = [];
                            }
                    
                            if (!empty($session['day'])) {
                                foreach ($session['day'] as $day) {
                                    $grouped[$slotname][] = [
                                        'day' => $day,
                                        'start_time' => $session['start_time'] ?? null,
                                        'end_time' => $session['end_time'] ?? null,
                                    ];
                                }
                            }
                        }
                    
                        // Convert to desired array structure
                        $result = [];
                        foreach ($grouped as $slotname => $sessions) {
                            $result[] = [
                                'slotname' => $slotname,
                                'sessions' => $sessions
                            ];
                        }
                    
                        return $result;
                    }
                    
                    
                    
                    $annualSessionsInput = $request->input('annual_sessions', []);
                    $annualSessions = transformSessionsBySlotname($annualSessionsInput);
                
                    
                    $annualPackage = [
                        'name' => $request->input('annual_name'),
                        'start_date' => $request->input('annual_startDate')[0] ?? null,
                        'end_date' => $request->input('annual_endDate')[0] ?? null,
                        'reduction_price' => $request->input('annual_reduction')[0] ?? 0,
                        'levels' => $request->input('annual_levels', []),
                        'max_slots' => $request->input('annual_slots'),
                        'sessions' => $annualSessions, // use transformed sessions here
                    ];
                    
                    
                    
                    // Quarterly Package (Multiple items)
                    $quarterlyPackage = [];
                    $quarterlyNames = $request->input('quarterly_name', []);
                    foreach ($quarterlyNames as $index => $name) {
                        $quarterlyPackage[] = [
                            'name' => $name,
                            'start_date' => $request->input('quarterly_start_date')[$index] ?? null,
                            'end_date' => $request->input('quarterly_end_date')[$index] ?? null,
                        ];
                    }
                    $quarterlyReduction = $request->input('quarterly_reduction')[0] ?? 0;
                    $quarterlylevels = $request->input('quarterly_levels', []);
                    $quarterlyslots = $request->input('quarterly_slots');
                    // $quarterlySessions = $request->input('quarterly_sessions', []);
                    
                    $quarterlySessionsInput = $request->input('quarterly_sessions', []);
                    $quarterlySessions = transformSessionsBySlotname($quarterlySessionsInput);
    
        
                    // Monthly Package
                    $monthlySessionsInput = $request->input('monthly_sessions', []);
                    $monthlySessions = transformSessionsBySlotname($monthlySessionsInput);

                    $monthlyPackage = [
                        'name' => $request->input('monthly_name'),
                        'start_date' => $request->input('monthly_startDate')[0] ?? null,
                        'end_date' => $request->input('monthly_endDate')[0] ?? null,
                        'reduction_price' => $request->input('monthly_reduction')[0] ?? 0,
                        'levels' => $request->input('monthly_levels', []),
                        'max_slots' => $request->input('monthly_slots'),
                        'sessions' => $monthlySessions,
                    ];
                
                
                
                }
                else{
                    // Annual Package
                    $annualPackage = [
                        'name' => $request->input('annual_name'),
                        'start_date' => $request->input('annual_startDate')[0] ?? null,
                        'end_date' => $request->input('annual_endDate')[0] ?? null,
                        'reduction_price' => $request->input('annual_reduction')[0] ?? 0,
                        'levels' => $request->input('annual_levels', []),
                        'max_slots' => $request->input('annual_slots'),
                        'sessions' => $request->input('annual_sessions', []),
                    ];
                    
                    // Quarterly Package (Multiple items)
                    $quarterlyPackage = [];
                    $quarterlyNames = $request->input('quarterly_name', []);
                    foreach ($quarterlyNames as $index => $name) {
                        $quarterlyPackage[] = [
                            'name' => $name,
                            'start_date' => $request->input('quarterly_start_date')[$index] ?? null,
                            'end_date' => $request->input('quarterly_end_date')[$index] ?? null,
                        ];
                    }
                    $quarterlyReduction = $request->input('quarterly_reduction')[0] ?? 0;
                    $quarterlylevels = $request->input('quarterly_levels', []);
                    $quarterlyslots = $request->input('quarterly_slots');
                    $quarterlySessions = $request->input('quarterly_sessions', []);
                    
                    // $rawLevels = $request->input('monthly_levels', []);
    
                    // // Filter out incomplete entries (ensure name, price, and description are present)
                    // $cleanLevels = collect($rawLevels)->filter(function ($level) {
                    //     return !empty($level['name']) && isset($level['price']) && !empty($level['description']);
                    // })->values()->all();
    
        
                    // Monthly Package
                    $monthlyPackage = [
                        'name' => $request->input('monthly_name'),
                        'start_date' => $request->input('monthly_startDate')[0] ?? null,
                        'end_date' => $request->input('monthly_endDate')[0] ?? null,
                        'reduction_price' => $request->input('monthly_reduction')[0] ?? 0,
                        'levels' => $request->input('monthly_levels', []),
                        'max_slots' => $request->input('monthly_slots'),
                        'sessions' => $request->input('monthly_sessions', []),
                    ];
                    
                }
    
                
                
        
                // Insert the event packages into event_packages table
                DB::table('event_packages')->insert([
                    'event_id' => $eventId,
                    'annual_details'        => json_encode($annualPackage),
                    'quarterly_details'     => json_encode([
                        'packages'          => $quarterlyPackage,
                        'reduction_price'   => $quarterlyReduction,
                        'levels'            => $quarterlylevels,
                        'max_slots'             => $quarterlyslots,
                        'sessions'          => $quarterlySessions,
                    ]),
                    'monthly_details'       => json_encode($monthlyPackage),
                    'created_at' => now(),
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Data processed',
                'status' => 'success',
                'event_id' => $eventId
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'failed',
                'message' => 'An error occurred during processing.',
                'details' => $e->getMessage(),
                'insert_status' => $results
            ], 500);
        }
    }
    
    public function newcourseopublish(Request $request, $id)
    {
        $course = DB::table('events')->where('id', $id)->first();

        if (!$course) {
            return redirect()->back()->with('error', 'Course not found.');
        }

        $action = $request->input('action');

        if ($action === 'publish') {
            if ($course->status == 1) {
                return redirect()->back()->with('error', 'Course online is already published.');
            }

            DB::table('events')->where('id', $id)->update([
                'status' => 1,
            ]);

            return redirect()->back()->with('success', 'Course published!');
        }

        if ($action === 'unpublish') {
            if ($course->status == 0) {
                return redirect()->back()->with('error', 'Course online is already unpublished.');
            }

            DB::table('events')->where('id', $id)->update([
                'status' => 2,
            ]);

            return redirect()->back()->with('success', 'Course online unpublished!');
        }

        return redirect()->back()->with('error', 'Invalid action.');
    }
    
    public function editnewcourseso(Request $request, $id)
    {
        // echo '<pre>';
        // print_r($request->all());
        // exit();
        $pageTitle = 'Edit New Courses Online';
    
        // Fetch categories and locations for dropdowns
        $categories = DB::table('categories')->get();
        $locations = DB::table('locations')->get();
    
        // Get event and related package and all nested items
        $rows = DB::table('events as e')
            ->leftJoin('event_packages as ep', 'ep.event_id', '=', 'e.id')
            ->leftJoin('course_milestones_online as m', 'm.course_id', '=', 'e.id')
            ->leftJoin('course_sprints_online as s', 's.milestone_id', '=', 'm.id')
            ->leftJoin('course_sessions_online as cn', 'cn.sprint_id', '=', 's.id')
            ->where('e.id', $id)
            ->select(
                'e.*',
                'ep.annual_details',
                'ep.quarterly_details',
                'ep.monthly_details',
                'm.id as milestone_id',
                'm.name as milestone_name',
                'm.description as milestone_description',
                'm.learningOutcome as milestone_learningOutcome',
                'm.start_date as milestone_start',
                'm.end_date as milestone_end',
                'm.price as milestone_price',
                'm.reduction_price as milestone_reduction_price',
                's.id as sprint_id',
                's.name as sprint_name',
                's.description as sprint_description',
                's.learningoutcome as sprint_learningOutcome',
                's.price as sprint_price',
                's.reduction_price as sprint_reduction_price',
                's.sequence as sprint_sequence',
                'cn.id as session_id',
                'cn.sequence as session_sequence',
                'cn.sessionname',
                'cn.sessionduration'
            )
            ->get();
            
        //      echo '<pre>';
        // print_r($rows);
        // exit();
    
        if ($rows->isEmpty()) {
            abort(404, 'Course not found');
        }
    
        $first = $rows->first();
        
        //  echo '<pre>';
        // print_r($first);
        // exit();
    
        // Build course object
        $course = (object) [
            'id' => $first->id,
            'title' => $first->title,
            'slug' => $first->slug,
            'description' => $first->description,
            'short_description' => $first->short_description,
            'learningoutcome' => $first->learningoutcome,
            'category_id' => $first->category_id,
            'location_id' => $first->location_id,
            'apartment_id' => $first->apartment_id,
            'age_min' => $first->age_min,
            'age_max' => $first->age_max,
            'cover_image' => $first->cover_image,
            'start_date' => $first->start_date,
            'end_date' => $first->end_date,
            'start_time' => $first->start_time,
            'end_time' => $first->end_time,
            'price' => $first->price,
            'seats' => $first->seats,
            'type' => $first->type,
            'status' => $first->status,
            'recurring_type' => $first->online_type,
        ];
        
        // echo '<pre>';
        //   echo 'ccc';
        // print_r($course);
        // exit();
        
    
        // Build milestones -> sprints -> sessions
        $milestones = $rows->groupBy('milestone_id')->map(function ($milestoneGroup, $milestoneId) {
            if (!$milestoneId) return null;
            $m = $milestoneGroup->first();
    
            $sprints = $milestoneGroup->groupBy('sprint_id')->map(function ($sprintGroup, $sprintId) {
                if (!$sprintId) return null;
                $s = $sprintGroup->first();
    
                $sessions = $sprintGroup->whereNotNull('session_id')->map(function ($session) {
                    return (object)[
                        'id' => $session->session_id,
                        'sequence' => $session->session_sequence,
                        'sessionname' => $session->sessionname,
                        'sessionduration' => $session->sessionduration,
                    ];
                })->values();
    
                return (object)[
                    'id' => $sprintId,
                    'name' => $s->sprint_name,
                    'description' => $s->sprint_description,
                    'learningoutcome' => $s->sprint_learningOutcome,
                    'price' => $s->sprint_price,
                    'reduction_price' => $s->sprint_reduction_price,
                    'sequence' => $s->sprint_sequence,
                    'sessions' => $sessions,
                ];
            })->filter()->values();
    
            return (object)[
                'id' => $milestoneId,
                'name' => $m->milestone_name,
                'description' => $m->milestone_description,
                'learningOutcome' => $m->milestone_learningOutcome,
                'start_date' => $m->milestone_start,
                'end_date' => $m->milestone_end,
                'price' => $m->milestone_price,
                'reduction_price' => $m->milestone_reduction_price,
                'sprints' => $sprints,
            ];
        })->filter()->values();
    
        // Decode packages
        $package = (object)[
            'annual_details' => json_decode($first->annual_details, true),
            'quarterly_details' => json_decode($first->quarterly_details, true),
            'monthly_details' => json_decode($first->monthly_details, true),
        ];
    
        // Decode sessions inside annual if needed
        if (!empty($package->annual_details['sessions']) && is_string($package->annual_details['sessions'])) {
            $package->annual_details['sessions'] = json_decode($package->annual_details['sessions'], true);
        }
        
        
        $courseMilestones = [];
        
        // Fetch courses and milestones
        try {
            $response = Http::get(env('APP_API') . 'api/new-course-list');
            if ($response->successful()) {
                $courses = $response->json()['data'];
                foreach ($courses as $cour) {
                    $courseMilestones[$cour['id']] = $cour['milestones'];
                }
            } else {
                Log::error('Courses API failed: ' . $response->status());
            }
        } catch (\Exception $e) {
            Log::error('Courses API exception: ' . $e->getMessage());
        }

        $assignment = DB::table('course_apartment as ca')
            ->leftJoin('events as c', 'ca.course_id', '=', 'c.id')
            ->leftJoin('apartment as a', 'ca.apartments_id', '=', 'a.id')
            ->leftJoin('course_facility as f', 'ca.facility_id', '=', 'f.id')
            ->leftJoin('batches as b', 'ca.batch_id', '=', 'b.id')
            ->leftJoin('batch_master as bm', 'bm.id', '=', 'b.batch_master_id')
            ->select(
                'ca.id as id',
                'ca.course_id',
                'c.title as course_name',
                'ca.apartments_id',
                'a.apartment_name as apartment_name',
                'ca.facility_id',
                'f.facility_name as facility_name',
                'ca.annual_package',
                'ca.quarterly_package',
                'ca.monthly_package',
                'ca.course_fees',
                'ca.course_reduction_fees',
                'ca.batch_id',
                'b.id as batches_id',
                'b.batch_master_id as ba_id',
                'bm.name as batch_name',
                'b.start_date as batch_start_date',
                'b.end_date as batch_end_date',
                'ca.start_date',
                'ca.end_date',
                'ca.status',
            )
            ->where('ca.id', $id)
            ->first();

            $assignment->annual_package = json_decode($assignment->annual_package, true);
            $assignment->quarterly_package = json_decode($assignment->quarterly_package, true);
            $assignment->monthly_package = json_decode($assignment->monthly_package, true);

        $milestones = DB::table('apartment_milestones as m')
            ->leftJoin('apartment_sprints as s', 's.apartment_milestone_id', '=', 'm.id')
            ->where('m.course_apartment_id', $id)
            ->select(
                'm.course_apartment_id',
                'm.id as milestone_id',
                'm.milestone_id as milestone_ref_id',
                'm.start_date',
                'm.end_date',
                'm.price as milestone_price',
                'm.reduction_price as milestone_reduction_price',
                's.sprint_id',
                's.price as sprint_price'
            )
            ->get();

            // Milestone + Sprint structure: Build nested data
            $milestoneRecords = DB::table('apartment_milestones as m')
                ->leftJoin('apartment_sprints as s', 's.apartment_milestone_id', '=', 'm.id')
                ->where('m.course_apartment_id', $id)
                ->select(
                    'm.id as milestone_id',
                    'm.milestone_id as milestone_ref_id',
                    'm.start_date',
                    'm.end_date',
                    'm.price as milestone_price',
                    'm.reduction_price as milestone_reduction_price',
                    's.sprint_id',
                    's.price as sprint_price',
                    's.reduction_price as sprint_reduction_price'
                )
                ->get();

            $assignedMilestones = [];

            foreach ($milestoneRecords as $record) {
                $milestoneId = $record->milestone_ref_id;

                if (!isset($assignedMilestones[$milestoneId])) {
                    $assignedMilestones[$milestoneId] = [
                        'start_date' => $record->start_date,
                        'end_date' => $record->end_date,
                        'milestone_price' => $record->milestone_price,
                        'milestone_reduction_price' => $record->milestone_reduction_price,
                        'sprints' => []
                    ];
                }

                if ($record->sprint_id) {
                    $assignedMilestones[$milestoneId]['sprints'][$record->sprint_id] = [
                        'selected' => true,
                        'price' => $record->sprint_price,
                        'reduction_price' => $record->sprint_reduction_price,
                    ];
                }
            }

        $rawSessions = DB::table('apartment_session')
            ->where('course_apartment_id', $id)
            ->get();

        $sessions = [];

        foreach ($rawSessions as $session) {
            $sessions[$session->day] = [
                'start_time' => $session->start_time,
                'end_time' => $session->end_time,
                'active' => true
            ];
        }
        

        //     $batches = DB::table('batch_master')->select('id','name')->get();
    
        //     return view('Template::organizer.event.edit-new-courses-assign', compact(
        //         'pageTitle',
        //         'apartments',
        //         'courses',
        //         'courseMilestones',
        //         'facilities',
        //         'assignment',
        //         'assignedMilestones',
        //         'batches',
        //         'milestones',
        //         'sessions'
        //     ));
        // }
        
        // echo '<pre>';
        // print_r($course);
        // // exit();
        // echo '<br>';
        // print_r($categories);
        // // exit();
        // // echo '<pre>';
        // print_r($locations);
        // // exit();
        // // echo '<pre>';
        // print_r($milestones);
        // // exit();
        //   echo '<pre>';
        // print_r($package);
        // exit();
    
        return view('Template::organizer.event.edit-new-courseso', [
            'pageTitle' => $pageTitle,
            'course' => $course,
            'categories' => $categories,
            'locations' => $locations,
            'milestones' => $milestones,
            'packages' => $package,
            'courseMilestones' => $courseMilestones,
            'assignedMilestones' => $assignedMilestones,
        ]);
    }
    
    public function editnewcoursesotest(Request $request, $id)
    {
        // echo '<pre>';
        // print_r($request->all());
        // exit();
        $pageTitle = 'Edit New Courses Online';
    
        // Fetch categories and locations for dropdowns
        $categories = DB::table('categories')->get();
        $locations = DB::table('locations')->get();
    
        // Get event and related package and all nested items
        $rows = DB::table('events as e')
            ->leftJoin('event_packages as ep', 'ep.event_id', '=', 'e.id')
            ->leftJoin('course_milestones_online as m', 'm.course_id', '=', 'e.id')
            ->leftJoin('course_sprints_online as s', 's.milestone_id', '=', 'm.id')
            ->leftJoin('course_sessions_online as cn', 'cn.sprint_id', '=', 's.id')
            ->where('e.id', $id)
            ->select(
                'e.*',
                'ep.annual_details',
                'ep.quarterly_details',
                'ep.monthly_details',
                'm.id as milestone_id',
                'm.name as milestone_name',
                'm.description as milestone_description',
                'm.learningOutcome as milestone_learningOutcome',
                'm.start_date as milestone_start',
                'm.end_date as milestone_end',
                'm.price as milestone_price',
                'm.reduction_price as milestone_reduction_price',
                's.id as sprint_id',
                's.name as sprint_name',
                's.description as sprint_description',
                's.learningoutcome as sprint_learningOutcome',
                's.price as sprint_price',
                's.reduction_price as sprint_reduction_price',
                's.sequence as sprint_sequence',
                'cn.id as session_id',
                'cn.sequence as session_sequence',
                'cn.sessionname',
                'cn.sessionduration'
            )
            ->get();
            
        //      echo '<pre>';
        // print_r($rows);
        // exit();
    
        if ($rows->isEmpty()) {
            abort(404, 'Course not found');
        }
    
        $first = $rows->first();
    
        // Build course object
        $course = (object) [
            'id' => $first->id,
            'title' => $first->title,
            'slug' => $first->slug,
            'description' => $first->description,
            'short_description' => $first->short_description,
            'learningoutcome' => $first->learningoutcome,
            'category_id' => $first->category_id,
            'location_id' => $first->location_id,
            'apartment_id' => $first->apartment_id,
            'age_min' => $first->age_min,
            'age_max' => $first->age_max,
            'cover_image' => $first->cover_image,
            'start_date' => $first->start_date,
            'end_date' => $first->end_date,
            'start_time' => $first->start_time,
            'end_time' => $first->end_time,
            'price' => $first->price,
            'type' => $first->type,
            'status' => $first->status,
            'recurring_type' => $first->online_type,
        ];
        
    
        // Build milestones -> sprints -> sessions
        $milestones = $rows->groupBy('milestone_id')->map(function ($milestoneGroup, $milestoneId) {
            if (!$milestoneId) return null;
            $m = $milestoneGroup->first();
    
            $sprints = $milestoneGroup->groupBy('sprint_id')->map(function ($sprintGroup, $sprintId) {
                if (!$sprintId) return null;
                $s = $sprintGroup->first();
    
                $sessions = $sprintGroup->whereNotNull('session_id')->map(function ($session) {
                    return (object)[
                        'id' => $session->session_id,
                        'sequence' => $session->session_sequence,
                        'sessionname' => $session->sessionname,
                        'sessionduration' => $session->sessionduration,
                    ];
                })->values();
    
                return (object)[
                    'id' => $sprintId,
                    'name' => $s->sprint_name,
                    'description' => $s->sprint_description,
                    'learningoutcome' => $s->sprint_learningOutcome,
                    'price' => $s->sprint_price,
                    'reduction_price' => $s->sprint_reduction_price,
                    'sequence' => $s->sprint_sequence,
                    'sessions' => $sessions,
                ];
            })->filter()->values();
    
            return (object)[
                'id' => $milestoneId,
                'name' => $m->milestone_name,
                'description' => $m->milestone_description,
                'learningOutcome' => $m->milestone_learningOutcome,
                'start_date' => $m->milestone_start,
                'end_date' => $m->milestone_end,
                'price' => $m->milestone_price,
                'reduction_price' => $m->milestone_reduction_price,
                'sprints' => $sprints,
            ];
        })->filter()->values();
    
        // Decode packages
        $package = (object)[
            'annual_details' => json_decode($first->annual_details, true),
            'quarterly_details' => json_decode($first->quarterly_details, true),
            'monthly_details' => json_decode($first->monthly_details, true),
        ];
    
        // Decode sessions inside annual if needed
        if (!empty($package->annual_details['sessions']) && is_string($package->annual_details['sessions'])) {
            $package->annual_details['sessions'] = json_decode($package->annual_details['sessions'], true);
        }
        
        // echo '<pre>';
        // print_r($course);
        // exit();
        // echo '<br>';
        // print_r($categories);
        // // exit();
        // // echo '<pre>';
        // print_r($locations);
        // // exit();
        // // echo '<pre>';
        // print_r($milestones);
        // // exit();
        // // echo '<pre>';
        // print_r($package);
        // exit();
    
        return view('Template::organizer.event.edit-new-courseso', [
            'pageTitle' => $pageTitle,
            'course' => $course,
            'categories' => $categories,
            'locations' => $locations,
            'milestones' => $milestones,
            'packages' => $package
        ]);
    }


    public function editnewcoursesotestt(Request $request, $id)
    {
        $pageTitle = 'Edit New Courses online';
        
        // 1. Get the main course/event
        $course = DB::table('events')->where('id', $id)->first();

        $categories = DB::table('categories')->get();
        $locations = DB::table('locations')->get();


        // 3. Get milestones for this course
        $milestones = DB::table('course_milestones_online')
                        ->where('course_id', $id)
                        ->orderBy('sequence')
                        ->get();
        
        // 4. For each milestone, get sprints and sessions
        $milestones = $milestones->map(function ($milestone) {
            $sprints = DB::table('course_sprints_online')
                        ->where('milestone_id', $milestone->id)
                        ->orderBy('sequence')
                        ->get();

            $sprints = $sprints->map(function ($sprint) {
                $sessions = DB::table('course_sessions_online')
                            ->where('sprint_id', $sprint->id)
                            ->orderBy('sequence')
                            ->get();
                $sprint->sessions = $sessions;
                return $sprint;
            });

            $milestone->sprints = $sprints;
            return $milestone;
        });
        
        $package = DB::table('event_packages')
                 ->where('event_id', $id)
                 ->first();

            if (!$package) {
                abort(404, 'Package not found');
            }
        
            // Decode the JSON fields to arrays or objects
            $package->annual_details = json_decode($package->annual_details, true);
            
            // Decode `levels` if it's a JSON string
                if (isset($package->annual_details['levels']) && is_string($package->annual_details['levels'])) {
                    $package->annual_details['levels'] = json_decode($package->annual_details['levels'], true);
                }
                
                // Decode 'sessions' if it's a JSON string
                if (!empty($package->annual_details['sessions']) && is_string($package->annual_details['sessions'])) {
                    $package->annual_details['sessions'] = json_decode($package->annual_details['sessions'], true);
                }
                
            $package->quarterly_details = json_decode($package->quarterly_details, true);
            $package->monthly_details = json_decode($package->monthly_details, true);
        
        // echo '<pre>';
        // print_r( $course);
        // echo '<br>';
        // print_r( $milestones);
        // echo '<br>';
        // print_r( $package);
        // exit();

        // 5. Pass data to view
        return view('Template::organizer.event.edit-new-courseso', [
            'pageTitle' => $pageTitle,
            'course' => $course,
            'milestones' => $milestones,
            'categories' => $categories,
            'locations' => $locations,
            'packages' =>$package
        ]);
    }

    public function updatenewcourseso(Request $request)
    {
        $courseId = $request->input('course_id');

        DB::beginTransaction();

        try {
            // Handle course image upload
            $courseImagePath = null;
            if ($request->hasFile('courseImage')) {
                $courseImagePath = fileUploader(
                    $request->file('courseImage'),
                    getFilePath('eventCover'),
                    getFileSize('eventCover'),
                    $request->input('old_course_image') ?? null
                );
            } else {
                $courseImagePath = $request->input('old_course_image');
            }

            // Update course details
            DB::table('events')->where('id', $courseId)->update([
                'organizer_id' => auth()->guard('organizer')->id(),
                'category_id' => $request->input('category_id'),
                'location_id' => $request->input('location_id'),
                'apartment_id' => $request->input('apartment_id'),
                'link' => $request->input('link'),
                'title' => $request->input('courseName'),
                'slug' => Str::slug($request->input('courseName')) . '-' . uniqid(),
                'location_address' => "test address",
                'short_description' => $request->input('shortDescription'),
                'description' => $request->input('description'),
                'learningoutcome' => $request->input('learningoutcome'),
                'age_min' => $request->input('min_age'),
                'age_max' => $request->input('max_age'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'start_time' => $request->input('start_time'),
                'end_time' => $request->input('end_time'),
                'seats' => $request->input('seats'),
                'seats_booked' => 0,
                'price' => $request->input('price'),
                'cover_image' => $courseImagePath,
                'type' => $request->input('course_type'),
                'online_type' => $request->input('recurring_type'),
                'is_featured' => $request->input('is_featured', 0),
                'step' => 0,
                'status' => 2,
                'online' => 1,
                'verification_details' => $request->input('verification_details', ''),
                'updated_at' => now(),
            ]);

            // Handle course type specific data
            if ($request->input('course_type') == 7) {
                // Handle milestones for milestone-based courses
                $milestones = json_decode($request->input('milestones'), true);

                if (!empty($milestones)) {
                    // Delete existing milestones and sprints for this course
                    DB::table('course_sessions_online')->whereIn('sprint_id', function($query) use ($courseId) {
                        $query->select('id')->from('course_sprints_online')->whereIn('milestone_id', function($subQuery) use ($courseId) {
                            $subQuery->select('id')->from('course_milestones_online')->where('course_id', $courseId);
                        });
                    })->delete();

                    DB::table('course_sprints_online')->whereIn('milestone_id', function($query) use ($courseId) {
                        $query->select('id')->from('course_milestones_online')->where('course_id', $courseId);
                    })->delete();

                    DB::table('course_milestones_online')->where('course_id', $courseId)->delete();

                    foreach ($milestones as $milestoneIndex => $milestoneData) {
                        $milestoneImagePath = null;
                        if ($request->hasFile("milestoneImage.$milestoneIndex")) {
                            $milestoneImagePath = fileUploader(
                                $request->file("milestoneImage.$milestoneIndex"),
                                getFilePath('eventCover'),
                                getFileSize('eventCover'),
                                null
                            );
                        }

                        $milestoneId = DB::table('course_milestones_online')->insertGetId([
                            'course_id' => $courseId,
                            'name' => $milestoneData['name'],
                            'sequence' => $milestoneIndex + 1,
                            'subjects' => json_encode($milestoneData['subjects'] ?? []),
                            'description' => $milestoneData['description'],
                            'learningoutcome' => $milestoneData['learningOutcome'],
                            'start_date' => $milestoneData['startDate'],
                            'end_date' => $milestoneData['endDate'],
                            'price' => $milestoneData['price'],
                            'reduction_price' => $milestoneData['reductionPrice'],
                            'milestoneImages' => $milestoneImagePath,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        // Handle sprints for this milestone
                        if (!empty($milestoneData['sprints'])) {
                            foreach ($milestoneData['sprints'] as $sprintIndex => $sprintData) {
                                $sprintImagePath = null;
                                if ($request->hasFile("sprintImage_{$milestoneIndex}_{$sprintIndex}")) {
                                    $sprintImagePath = fileUploader(
                                        $request->file("sprintImage_{$milestoneIndex}_{$sprintIndex}"),
                                        getFilePath('eventCover'),
                                        getFileSize('eventCover'),
                                        null
                                    );
                                }

                                $sprintId = DB::table('course_sprints_online')->insertGetId([
                                    'milestone_id' => $milestoneId,
                                    'name' => $sprintData['theme'],
                                    'sequence' => $sprintData['sequence'],
                                    'no_of_sections' => count($sprintData['sections'] ?? []),
                                    'sprintImages' => $sprintImagePath,
                                    'description' => $sprintData['description'],
                                    'learningoutcome' => $sprintData['learningOutcome'],
                                    'price' => $sprintData['price'],
                                    'reduction_price' => $sprintData['reductionPrice'],
                                    'slots' => $sprintData['slots'] ?? 0,
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ]);

                                // Handle sections/sessions for this sprint
                                if (!empty($sprintData['sections'])) {
                                    foreach ($sprintData['sections'] as $sessionIndex => $session) {
                                        DB::table('course_sessions_online')->insert([
                                            'sprint_id' => $sprintId,
                                            'sequence' => $session['sequence'],
                                            'sessionname' => $session['name'],
                                            'sessionduration' => $session['duration'],
                                            'created_at' => now(),
                                            'updated_at' => now(),
                                        ]);
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                // Handle packages for recurring courses
                $annualPackage = [];
                $quarterlyPackage = [];
                $monthlyPackage = [];

                // Process annual package
                if ($request->input('annual_name')) {
                    $annualPackage = [
                        'name' => $request->input('annual_name'),
                        'start_date' => $request->input('annual_startDate.0'),
                        'end_date' => $request->input('annual_endDate.0'),
                        'reduction_price' => $request->input('annual_reduction.0'),
                        'levels' => json_decode($request->input('annual_levels'), true) ?? [],
                        'max_slots' => $request->input('annual_slots'),
                        'sessions' => json_decode($request->input('annual_sessions'), true) ?? [],
                        'image' => null
                    ];

                    if ($request->hasFile('annual_image')) {
                        $annualPackage['image'] = fileUploader(
                            $request->file('annual_image'),
                            getFilePath('eventCover'),
                            getFileSize('eventCover'),
                            null
                        );
                    }
                }

                // Process quarterly package
                $quarterlyNames = $request->input('quarterly_name', []);
                $quarterlyStartDates = $request->input('quarterly_start_date', []);
                $quarterlyEndDates = $request->input('quarterly_end_date', []);

                $quarterlyPackages = [];
                if (!empty($quarterlyNames)) {
                    foreach ($quarterlyNames as $index => $name) {
                        if (!empty($name)) {
                            $quarterlyPackages[] = [
                                'name' => $name,
                                'start_date' => $quarterlyStartDates[$index] ?? '',
                                'end_date' => $quarterlyEndDates[$index] ?? ''
                            ];
                        }
                    }
                }

                $quarterlyPackage = [
                    'packages' => $quarterlyPackages,
                    'reduction_price' => $request->input('quarterly_reduction.0'),
                    'levels' => [], // Add quarterly levels processing if needed
                    'max_slots' => $request->input('quarterly_slots'),
                    'sessions' => [], // Add quarterly sessions processing if needed
                ];

                // Process monthly package
                if ($request->input('monthly_name')) {
                    $monthlyPackage = [
                        'name' => $request->input('monthly_name'),
                        'start_date' => $request->input('monthly_startDate.0'),
                        'end_date' => $request->input('monthly_endDate.0'),
                        'reduction_price' => $request->input('monthly_reduction.0'),
                        'levels' => [], // Add monthly levels processing if needed
                        'max_slots' => $request->input('monthly_slots'),
                        'sessions' => [], // Add monthly sessions processing if needed
                    ];
                }

                // Update or insert event packages
                $existingPackage = DB::table('event_packages')->where('event_id', $courseId)->first();

                if ($existingPackage) {
                    DB::table('event_packages')->where('event_id', $courseId)->update([
                        'annual_details' => json_encode($annualPackage),
                        'quarterly_details' => json_encode($quarterlyPackage),
                        'monthly_details' => json_encode($monthlyPackage),
                        'updated_at' => now(),
                    ]);
                } else {
                    DB::table('event_packages')->insert([
                        'event_id' => $courseId,
                        'annual_details' => json_encode($annualPackage),
                        'quarterly_details' => json_encode($quarterlyPackage),
                        'monthly_details' => json_encode($monthlyPackage),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'message' => 'Course updated successfully',
                'status' => 'success',
                'event_id' => $courseId
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'failed',
                'message' => 'An error occurred during processing.',
                'details' => $e->getMessage(),
            ], 500);
        }
    }

    public function editnewcousreomilestonedestroy(Request $request)
    {
        $id = $request->input('id');

        try {
            DB::transaction(function () use ($id) {
                // Delete sessions associated with sprints of the milestone
                DB::table('course_sessions')->whereIn('sprint_id', function($query) use ($id) {
                    $query->select('id')->from('course_sprints')->where('milestone_id', $id);
                })->delete();

                // Delete sprints associated with the milestone
                DB::table('course_sprints')->where('milestone_id', $id)->delete();

                // Finally, delete the milestone
                DB::table('course_milestones')->where('id', $id)->delete();
            });

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while deleting the milestone.',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    public function editnewcousreosprintdestroy(Request $request)
    {
        $id = $request->input('id');

        try {
            DB::transaction(function () use ($id) {
                // Delete sessions associated with the sprint
                DB::table('course_sessions')->where('sprint_id', $id)->delete();

                // Delete the sprint
                DB::table('course_sprints')->where('id', $id)->delete();
            });
            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while deleting the milestone sprint.',
                'details' => $e->getMessage()
            ], 500);
        }
    }
    
    


    
}
