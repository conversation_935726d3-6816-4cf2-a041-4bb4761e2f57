@extends($activeTemplate . 'organizer.layouts.master')
@section('content')
<div class="row">
    <div class="col-lg-12">
    <form id="courseForm">
                    @csrf

        <!-- Course Info -->
        <div class="card shadow-sm border-0 mb-4">
        
            <div class="card-header">
                <h5 class="">{{ __($pageTitle) }}</h5>
            </div>
            <div class="card-body">
            
                <!-- Course Type Radio Buttons -->
                <div class="mb-3">
                    <label class="form-label">Course Type</label>
                    <div>
                        <div class="form-check form-check-inline">
                        <!-- Online Recurring = coursetype = 6 Online Milestone = coursetype = 7 -->
                            <input class="form-check-input" type="radio" name="course_type" id="course_type" value="onlinerecurring" checked>
                            <label class="form-check-label" for="onlinerecurring">Online Recurring</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="course_type" id="course_type" value="onlinemilestone">
                            <label class="form-check-label" for="onlinemilestone">Online Milestone</label>
                        </div>
                    </div>
                </div>
                <!-- 1 to 1 / 1 to many toggle section -->
                <div id="recurringOptions" style="display: none;" class="mb-3">
                    <label class="form-label">Recurring Type</label>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="recurring_type" id="one_to_one" value="1to1" checked>
                            <label class="form-check-label" for="one_to_one">1 to 1</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="recurring_type" id="one_to_many" value="1tomany">
                            <label class="form-check-label" for="one_to_many">1 to many</label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <!-- Category Dropdown -->
                    <div class="col-md-6 mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select name="category_id" id="category_id" class="form-select" required>
                            <option value="">Select Category</option>
                            @foreach($categories as $cat)
                            <option value="{{ $cat['id'] }}">{{ $cat['name'] }}</option>
                            @endforeach
                        
                        </select>
                    </div>

                    <!-- Location Dropdown -->
                    <div class="col-md-6 mb-3">
                        <label for="location" class="form-label">Location</label>
                        <select name="location_id" id="location_id" class="form-select" required>
                            <option value="">Select Location</option>
                            @foreach($locations as $loc)
                            <option value="{{ $loc['id'] }}" >{{ $loc['name'] }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="courseName" class="form-label">Course Name</label>
                        <input type="text" class="form-control" id="courseName" name="courseName" placeholder="Enter course name">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="shortDescription" class="form-label">Short Description</label>
                        <textarea class="form-control" id="shortDescription" name="shortDescription" placeholder="Short description"></textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2" placeholder="Description"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="learningoutcome" class="form-label">Learning Outcome</label>
                        <textarea class="form-control" id="learningoutcome" name="learningoutcome" rows="2" placeholder="Learning outcome"></textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="min_age" class="form-label">Min Age</label>
                        <input type="number" name="min_age" id="age_min" class="form-control" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="max_age" class="form-label">Max Age</label>
                        <input type="number" name="max_age" id="age_max" class="form-control" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="price" class="form-label">Price</label>
                        <input type="number" class="form-control" id="price" name="price" placeholder="Enter price">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="seats" class="form-label">Total Seats</label>
                        <input type="number" name="seats" id="seats" class="form-control" placeholder="Enter seats">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="courseImage" class="form-label">Course Image</label>
                    <input type="file" class="form-control" id="courseImage" name="courseImage">
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" name="start_date" id="start_date" class="form-control" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" name="end_date" id="end_date" class="form-control" required>
                    </div>
                </div>
                <div id="packageFields" style="display: none;">
                    <!-- Annual package-->
                    <div class="AnnualpackageList">
                        <h6>Annual package</h6>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="annualName" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="annualName" name="annual_name" placeholder="Name">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" name="annual_startDate[]">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control" name="annual_endDate[]">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Reduction Price (₹)</label>
                                <input type="number" class="form-control" name="annual_reduction[]" placeholder="Price">
                            </div>
                            <div class="row">
                                <h6>Level</h6>
                                <div id="annualLevelsContainer">
                                    <div class="row level-item mb-3">
                                        <div class="col-md-4">
                                            <label class="form-label">Name</label>
                                            <input type="text" class="form-control" name="annual_levels[0][name]" placeholder="Level name">
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" name="aannual_levels[0][description]" rows="2" placeholder="Description"></textarea>
                                        </div>
                                        
                                        <div class="col-md-2">
                                            <label class="form-label">Price</label>
                                            <input type="number" class="form-control" name="annual_levels[0][price]" placeholder="Price">
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <!-- Remove button shown only on dynamically added rows -->
                                        </div>
                                    </div>
                                </div>
                            
                                <div class="mb-3">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addLevelRow('annual')">+ Add More</button>
                                </div>
                            
                                <!-- Common fields for all levels -->
                                <div class="row">
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" class="form-control" name="annual_image">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Max. Slots/Week</label>
                                        <input type="number" class="form-control" name="annual_slots" placeholder="">
                                    </div>
                                </div>
                            </div>

                            
                            <div class="form-group mt-4" id="sessionSchedule_1to1_annual">
                                <label>Session Schedule</label>
                                <div >
                                    <!-- First row -->
                                    <div class="row align-items-center mb-2 session-row">
                                        <div class="col-md-3">
                                            <label>Day</label>
                                            <select class="form-select" name="annual_sessions[0][day]">
                                                <option value="">Select Day</option>
                                                @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                    <option value="{{ $day }}">{{ $day }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label>Start Time</label>
                                            <input type="time" class="form-control" name="annual_sessions[0][start_time]" />
                                        </div>
                                        <div class="col-md-3">
                                            <label>End Time</label>
                                            <input type="time" class="form-control" name="annual_sessions[0][end_time]" />
                                        </div>
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRow('annual')">+ Add More</button>
                            </div>
                            
                            <div class="form-group mt-4" id="sessionSchedule_1tomany_annual">
                                <label>Session Schedule</label>
                                <div >
                                    <!-- First row -->
                                    <div class="row align-items-center mb-2 session-row">
                                        <div class="col-md-12 mb-2">
                                            <label class="form-label">Slot name</label>
                                            <input type="text" class="form-control" name="annual_sessions[0][slotname]" placeholder="Slot name">
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Day</label>
                                                @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" name="annual_sessions[0][day][]" value="{{ $day }}"> {{ $day }}
                                                    </div>
                                                @endforeach
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">Start Time</label>
                                            <input type="time" class="form-control" name="annual_sessions[0][start_time]" />
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">End Time</label>
                                            <input type="time" class="form-control" name="annual_sessions[0][end_time]" />
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRowM('annual')">+ Add More</button>
                            </div>

        
                        </div>
                    </div>
                    <!-- Quarterly package-->
                    <div class="QuarterlypackageList">
                        <h6>Quarterly package</h6>
        
                        <div id="quarterlyPackageWrapper">
                            <div class="quarterly-package-item row g-3">
                                <div class="col-md-3">
                                    <label>Name</label>
                                    <input type="text" class="form-control" name="quarterly_name[]" placeholder="Name" >
                                </div>
                                <div class="col-md-3">
                                    <label>Start Date</label>
                                    <input type="date" class="form-control" name="quarterly_start_date[]" >
                                </div>
                                <div class="col-md-3">
                                    <label>End Date</label>
                                    <input type="date" class="form-control" name="quarterly_end_date[]" >
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-danger btn-sm removeQuarterly">X</button>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-primary" id="addQuarterly">+ Add More</button>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Reduction Price (₹)</label>
                                <input type="number" class="form-control" name="quarterly_reduction[]" placeholder="Price">
                            </div>
                            
                            <div class="row">
                                <h6>Level</h6>
                                <div id="quarterlyLevelsContainer">
                                    <div class="row level-item mb-3">
                                        <div class="col-md-4">
                                            <label class="form-label">Name</label>
                                            <input type="text" class="form-control" name="quarterly_levels[0][name]" placeholder="Level name">
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" name="quarterly_levels[0][description]" rows="2" placeholder="Description"></textarea>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Price</label>
                                            <input type="number" class="form-control" name="quarterly_levels[0][price]" placeholder="Price">
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <!-- Remove button shown only on dynamically added rows -->
                                        </div>
                                    </div>
                                </div>
                            
                                <div class="mb-3">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addLevelRow('quarterly')">+ Add More</button>
                                </div>
                            
                                <!-- Common fields for all levels -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" class="form-control" name="quarterly_image">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Max. Slots/Week</label>
                                        <input type="number" class="form-control" name="quarterly_slots" placeholder="">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group mt-4" id="sessionSchedule_1to1_quarterly">
                                <label>Session Schedule</label>
                                <div >
                                    <!-- First row -->
                                    <div class="row align-items-center mb-2 session-row">
                                        <div class="col-md-3">
                                            <label>Day</label>
                                            <select class="form-select" name="quarterly_sessions[0][day]">
                                                <option value="">Select Day</option>
                                                @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                    <option value="{{ $day }}">{{ $day }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label>Start Time</label>
                                            <input type="time" class="form-control" name="quarterly_sessions[0][start_time]" />
                                        </div>
                                        <div class="col-md-3">
                                            <label>End Time</label>
                                            <input type="time" class="form-control" name="quarterly_sessions[0][end_time]" />
                                        </div>
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRow('quarterly')">+ Add More</button>
                            </div>
                            
                            <div class="form-group mt-4" id="sessionSchedule_1tomany_quarterly">
                                <label>Session Schedule</label>
                                <div >
                                    <!-- First row -->
                                    <div class="row align-items-center mb-2 session-row">
                                    
                                        <div class="col-md-12 mb-2">
                                            <label class="form-label">Slot name</label>
                                            <input type="text" class="form-control" name="quarterly_sessions[0][slotname]" placeholder="Slot name">
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Day</label>
                                                @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" name="quarterly_sessions[0][day][]" value="{{ $day }}"> {{ $day }}
                                                    </div>
                                                @endforeach
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">Start Time</label>
                                            <input type="date" class="form-control" name="quarterly_sessions[0][start_time]" >
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">End Time</label>
                                            <input type="time" class="form-control" name="quarterly_sessions[0][end_time]" />
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRowM('quarterly')">+ Add More</button>
                            </div>
                            
                        </div>
                    </div>
                    <!-- Monthly package-->
                    <div class="MonthlypackageList">
                        <h6>Monthly package</h6>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="monthlyName" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="monthlyName" name="monthly_name" placeholder="Name">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" name="monthly_startDate[]">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control" name="monthly_endDate[]">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Reduction Price (₹)</label>
                                <input type="number" class="form-control" name="monthly_reduction[]" placeholder="Price">
                            </div>
                            
                            <div class="row">
                                <h6>Level</h6>
                                
                                <div id="monthlyLevelsContainer">
                                    <div class="row level-item mb-3">
                                        <div class="col-md-4">
                                            <label class="form-label">Name</label>
                                            <input type="text" class="form-control" name="monthly_levels[0][name]" placeholder="Level name">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" name="monthly_levels[0][description]" rows="2" placeholder="Description"></textarea>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Price</label>
                                            <input type="number" class="form-control" name="monthly_levels[0][price]" placeholder="Price">
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <!-- Remove button shown only on dynamically added rows -->
                                        </div>
                                    </div>
                                </div>
                            
                                <div class="mb-3">
                                     <button type="button" class="btn btn-success btn-sm" onclick="addLevelRow('monthly')">+ Add More</button>
                                </div>
                            
                                <!-- Common fields for all levels -->
                                <div class="row">
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" class="form-control" name="monthly_image">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Max. Slots/Week</label>
                                        <input type="number" class="form-control" name="monthly_slots" placeholder="">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mt-4" id="sessionSchedule_1to1_monthly">
                                <label>Session Schedule</label>
                                <div >
                                    <!-- First row -->
                                    <div class="row align-items-center mb-2 session-row">
                                        <div class="col-md-3">
                                            <label>Day</label>
                                            <select class="form-select" name="monthly_sessions[0][day]">
                                                <option value="">Select Day</option>
                                                @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                    <option value="{{ $day }}">{{ $day }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label>Start Time</label>
                                            <input type="time" class="form-control" name="monthly_sessions[0][start_time]" />
                                        </div>
                                        <div class="col-md-3">
                                            <label>End Time</label>
                                            <input type="time" class="form-control" name="monthly_sessions[0][end_time]" />
                                        </div>
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRow('monthly')">+ Add More</button>
                            </div>
                            
                            <div class="form-group mt-4" id="sessionSchedule_1tomany_monthly">
                                <label>Session Schedule</label>
                                <div>
                                    <!-- First row -->
                                    <div class="row align-items-center mb-2 session-row">
                                        <div class="col-md-12 mb-2">
                                            <label class="form-label">Slot name</label>
                                            <input type="text" class="form-control" name="monthly_sessions[0][slotname]" placeholder="Slot name">
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Day</label>
                                                @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" name="monthly_sessions[0][day][]" value="{{ $day }}"> {{ $day }}
                                                    </div>
                                                @endforeach
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">Start Time</label>
                                            <input type="time" class="form-control" name="monthly_sessions[0][start_time]" />
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">End Time</label>
                                            <input type="time" class="form-control" name="monthly_sessions[0][end_time]" />
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRowM('monthly')">+ Add More</button>
                            </div>
                            
                        </div>
                    </div>
                </div>
               
            </div>
        </div>
        <!-- Milestone Container -->
        <div id="milestoneSection" style="display: none;">
            <!-- Milestone Container -->
            <div id="milestoneContainer"></div>
            <!-- <button class="btn btn-success mb-3" onclick="addMilestone()">+ Add Milestone</button> -->
            <button type="button" class="btn btn-success mb-3" onclick="addMilestone()">+ Add Milestone</button>
        </div>
        
        <div class="mt-4" style="float:right">
            <!-- <button class="btn btn-primary" onclick="saveCourse()">Save Course</button> -->
            <button type="submit" class="btn btn-primary mt-3">Save Course</button>
        </div>
        </form>
          
    </div>
</div>

<x-confirmation-modal />
@endsection

@push('breadcrumb-plugins')
    <x-search-form placeholder="Name | Organizer" />
@endpush

@push('script')
<!-- Include Axios from CDN -->
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const typeRadios = document.querySelectorAll('input[name="course_type"]');
        const milestoneSection = document.getElementById('milestoneSection');
        const packageFields = document.getElementById('packageFields');
        const recurringOptions = document.getElementById('recurringOptions');
    
        function handleCourseTypeChange(value) {
            if (value === 'onlinemilestone') {
                milestoneSection.style.display = 'block';
                packageFields.style.display = 'none';
                recurringOptions.style.display = 'none';
            } else if (value === 'onlinerecurring') {
                
                milestoneSection.style.display = 'none';
                packageFields.style.display = 'block';
                recurringOptions.style.display = 'block'; // Show the new toggle options
            } else {
                milestoneSection.style.display = 'none';
                packageFields.style.display = 'block';
                recurringOptions.style.display = 'none';
            }
        }
    
        // Attach listener to all radios
        typeRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                handleCourseTypeChange(this.value);
            });
        });
    
        // Tigger logic once on load using the checked radio
        const selectedRadio = document.querySelector('input[name="course_type"]:checked');
        if (selectedRadio) {
            handleCourseTypeChange(selectedRadio.value);
        }
    });
    document.addEventListener('DOMContentLoaded', function () {
        const recurringRadios = document.querySelectorAll('input[name="recurring_type"]');
        
        const div1to1_annual = document.getElementById('sessionSchedule_1to1_annual');
        const div1toMany_annual = document.getElementById('sessionSchedule_1tomany_annual');
        
        const div1to1_quarterly = document.getElementById('sessionSchedule_1to1_quarterly');
        const div1toMany_quarterly = document.getElementById('sessionSchedule_1tomany_quarterly');
        
        const div1to1_monthly = document.getElementById('sessionSchedule_1to1_monthly');
        const div1toMany_monthly = document.getElementById('sessionSchedule_1tomany_monthly');
    
        function handleRecurringChange(value)
        {
            if (value === '1to1') {
                div1to1_annual.style.display = 'block';
                div1toMany_annual.style.display = 'none';
                
                div1to1_quarterly.style.display = 'block';
                div1toMany_quarterly.style.display = 'none';
                
                div1to1_monthly.style.display = 'block';
                div1toMany_monthly.style.display = 'none';
                
            } else if (value === '1tomany') {
                div1to1_annual.style.display = 'none';
                div1toMany_annual.style.display = 'block';
                
                div1to1_quarterly.style.display = 'none';
                div1toMany_quarterly.style.display = 'block';
                
                div1to1_monthly.style.display = 'none';
                div1toMany_monthly.style.display = 'block';
            } else {
                div1toMany_annual.style.display = 'none';
                div1toMany_quarterly.style.display = 'none';
                div1toMany_monthly.style.display = 'none';
                
                div1to1_annual.style.display = 'none';
                div1to1_quarterly.style.display = 'none';
                div1to1_monthly.style.display = 'none';
            }
        }
    
        recurringRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                handleRecurringChange(this.value);
            });
        });
    
        // Trigger once on load
        const selectedRecurring = document.querySelector('input[name="recurring_type"]:checked');
        if (selectedRecurring) {
            handleRecurringChange(selectedRecurring.value);
        }
    });
</script>

<script>
    document.getElementById('addQuarterly').addEventListener('click', function () {
        const wrapper = document.getElementById('quarterlyPackageWrapper');
        const newItem = document.createElement('div');
        newItem.className = 'quarterly-package-item row g-3 mt-2';
    
        newItem.innerHTML = `
            <div class="col-md-3">
            <label for="monthlyName" class="form-label">Name</label>
                <input type="text" class="form-control" name="quarterly_name[]" placeholder="Name" >
            </div>
            <div class="col-md-3">
                <label class="form-label">Start Date</label>
                <input type="date" class="form-control" name="quarterly_start_date[]" >
            </div>
            <div class="col-md-3">
                <label class="form-label">End Date</label>
                <input type="date" class="form-control" name="quarterly_end_date[]" >
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm removeQuarterly">X</button>
            </div>
        `;
        wrapper.appendChild(newItem);
    });
    
    document.addEventListener('click', function (e) {
        if (e.target && e.target.classList.contains('removeQuarterly')) {
            e.target.closest('.quarterly-package-item').remove();
        }
    });
</script>
<script>
    let levelIndices = {
        annual: 1,
        quarterly: 1,
        monthly: 1
    };

    function addLevelRow(packageType) {
        const container = document.getElementById(`${packageType}LevelsContainer`);
        const index = levelIndices[packageType];

        const row = document.createElement('div');
        row.className = 'row level-item mb-2';
        row.innerHTML = `
            <div class="col-md-4">
                <input type="text" class="form-control" name="${packageType}_levels[${index}][name]" placeholder="Level Name">
            </div>
            <div class="col-md-4">
                <textarea class="form-control" name="${packageType}_levels[${index}][description]" rows="2" placeholder="Description"></textarea>
            </div>
            <div class="col-md-2">
                <input type="number" class="form-control" name="${packageType}_levels[${index}][price]" placeholder="Price">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeLevelRow(this)">Remove</button>
            </div>
        `;
        container.appendChild(row);
        levelIndices[packageType]++;
    }

    function removeLevelRow(button) {
        button.closest('.level-item').remove();
    }
</script>
<script>
    const sessionIndexes = {
        annual: 1,
        quarterly: 1,
        monthly: 1
    };

    function addSessionRow(packageType) {
        const container = document.getElementById(`sessionSchedule_1to1_${packageType}`);
        const index = sessionIndexes[packageType]++;
        const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        const row = document.createElement('div');
        row.classList.add('row', 'align-items-center', 'mb-2', 'session-row');

        row.innerHTML = `
            <div class="col-md-3">
                <select class="form-select" name="${packageType}_sessions[${index}][day]">
                    <option value="">Select Day</option>
                    ${weekdays.map(day => `<option value="${day}">${day}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][start_time]" />
            </div>
            <div class="col-md-3">
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][end_time]" />
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeSessionRow(this)">Remove</button>
            </div>
        `;

        container.appendChild(row);
    }
    
    function addSessionRowM(packageType)
    {
        const container = document.getElementById(`sessionSchedule_1tomany_${packageType}`);
        const index = sessionIndexes[packageType]++;
    
        const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
        const row = document.createElement('div');
        row.classList.add('row', 'align-items-center', 'mb-2', 'session-row');
    
        row.innerHTML = `
            <div class="col-md-12 mb-2">
                <strong>${packageType}-Slots ${index + 1}</strong>
            </div>
    
            <div class="col-md-12 mb-2">
                <label class="form-label">Slot name</label>
                <input type="text" class="form-control" name="${packageType}_sessions[${index}][slotname]" placeholder="Slot name">
            </div>
    
            <div class="col-md-12 mb-2">
                <label class="form-label">Day</label>
                ${days.map(day => `
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" name="${packageType}_sessions[${index}][day][]" value="${day}"> ${day}
                    </div>
                `).join('')}
            </div>
    
            <div class="col-md-5 mb-2">
                <label class="form-label">Start Time</label>
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][start_time]" />
            </div>
    
            <div class="col-md-5 mb-2">
                <label class="form-label">End Time</label>
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][end_time]" />
            </div>
    
            <div class="col-md-2 mb-2">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
            </div>
        `;
    
        container.appendChild(row);
    }

    function removeSessionRow(button) {
        button.closest('.session-row').remove();
    }
</script>

<script>
    let milestoneCount = 0;
    const sprintCounters = {};

    function addMilestone() {
        const container = document.getElementById('milestoneContainer');
        
        // Calculate the next milestone number based on how many are currently in the DOM
        const currentMilestones = container.querySelectorAll('.card.shadow-sm.border');
        const milestoneIndex = currentMilestones.length;
        const milestoneId = `milestone_${Date.now()}`;
        const sprintContainerId = `${milestoneId}_sprints`;

        const milestoneHTML = `
            <div class="card shadow-sm border mb-4" id="${milestoneId}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0 text-primary">Milestone ${milestoneIndex + 1}</h5>
                        <button class="btn btn-sm btn-danger" onclick="removeElement('${milestoneId}')">Remove</button>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-12">
                            <label for="milestoneName_${milestoneId}" class="form-label">Milestone Name</label>
                            <input type="text" class="form-control mb-2" id="milestoneName_${milestoneId}" name="milestoneName[]" placeholder="Milestone Name">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="milestoneDesc_${milestoneId}" class="form-label">Milestone Description</label>
                            <textarea class="form-control mb-2" id="milestoneDesc_${milestoneId}" name="milestoneDesc[]" placeholder="Milestone Description"></textarea>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="learningOutcome_${milestoneId}" class="form-label">Learning Outcome</label>
                            <textarea class="form-control mb-3" id="learningOutcome_${milestoneId}" name="learningOutcome[]" placeholder="Learning Outcome"></textarea>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="form-label">Image</label>
                            <input type="file" class="form-control mb-3" name="milestoneImage[]">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="subjects_${milestoneId}" class="form-label">Subjects</label>
                            <select class="form-control select2 mb-3" id="subjects_${milestoneId}" name="subjects[${milestoneId}][]" multiple>
                                <option value="Communication">Communication</option>
                                <option value="Arts">Arts</option>
                                <option value="Maths">Maths</option>
                                <option value="PD">PD</option>
                                <option value="STEM">STEM</option>
                            </select>
                        </div>
                        
                        <div class="form-group col-md-3">
                            <label>Start Date</label>
                            <input type="date" name="milestone_startDate[]" class="form-control" />
                        </div>
                        <div class="form-group col-md-3">
                            <label>End Date</label>
                            <input type="date" name="milestone_endDate[]" class="form-control" />
                        </div>
                        <div class="form-group col-md-3">
                            <label>Milestone Price</label>
                            <input type="number" name="milestone_price[]" class="form-control" />
                        </div>
                        <div class="form-group col-md-3">
                            <label>Milestone Reduction Price</label>
                            <input type="number" name="milestone_reductionPrice[]" class="form-control" />
                        </div>
                    </div>
                    
                    <div id="${sprintContainerId}"></div>
                    <button type="button" class="btn btn-primary btn-sm mt-2" onclick="addSprint('${sprintContainerId}', '${milestoneId}')">+ Add Sprint</button>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', milestoneHTML);
        $('.select2').select2();
        sprintCounters[milestoneId] = 0;
        renumberMilestones(); // Add this line
    }

    function addSprint(targetId, milestoneId) {
        if (!sprintCounters[milestoneId]) sprintCounters[milestoneId] = 0;
        const sprintNum = ++sprintCounters[milestoneId];
        const sprintId = `sprint_${milestoneId}_${sprintNum}_${Date.now()}`;
        const sectionContainerId = `${sprintId}_sections`;

        const sprintHTML = `
            <div class="card border mb-3 mt-3 sprint-card" id="${sprintId}">
                <div class="card-body bg-light">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Sprint ${sprintNum}:</h6>
                        <button class="btn btn-sm btn-danger" onclick="removeElement('${sprintId}')">Delete Sprint</button>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-control" name="sprints[${milestoneId}][${sprintNum}][theme]" placeholder="Theme Name" style="width: 200px;">
                        </div>
                        <div class="col-md-9">
                            <label class="form-label">Image</label>
                            <input type="file" class="form-control mb-3" name="sprintImage_${milestoneId}_${sprintNum}">
                        </div>
                    </div> 
                    <div class="row mb-3">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control mb-2" name="sprintDesc[]" placeholder="Sprint Description"></textarea>
                        </div>  
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Learning Outcome</label>
                            <textarea class="form-control mb-3" name="sprintlearningOutcome[]" placeholder="Learning Outcome"></textarea>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label>Sprints Price</label>
                            <input type="number" name="sprintprice[]" class="form-control" />
                        </div>
                        <div class="col-md-4">
                            <label>Sprints Reduction Price</label>
                            <input type="number" name="sprintreductionprice[]" class="form-control" />
                        </div>
                        <div class="col-md-4">
                            <label>Sprints Max. Slots/Week</label>
                            <input type="number" name="sprintslots[]" class="form-control" />
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Number of Sections</label>
                        <input type="number" class="form-control w-25" name="sprints[${milestoneId}][${sprintNum}][sectionCount]" placeholder="e.g. 3" oninput="generateSections('${sectionContainerId}', this.value)">
                    </div>

                    <div id="${sectionContainerId}"></div>
                    
                            <div class="form-group mt-4">
                                <label>Session Schedule</label>
                                <div class="row">
                                    <div class="row fw-bold mb-2">
                                        <div class="col-md-2">Day</div>
                                        <div class="col-md-5">Start Time</div>
                                        <div class="col-md-4">End Time</div>
                                    </div>
                                    @php
                                        $weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                                    @endphp

                                    @foreach($weekdays as $day)
                                        <div class="col-12 mb-2">
                                            <div class="row align-items-start">
                                                <div class="col-md-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="day_{{ strtolower($day) }}" name="">
                                                        <label class="form-check-label" for="day_{{ strtolower($day) }}">{{ $day }}</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-5">
                                                    <input type="time" class="form-control form-control-sm" name="">
                                                </div>
                                                <div class="col-md-5">
                                                    <input type="time" class="form-control form-control-sm" name="">
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                </div>
            </div>
        `;
        document.getElementById(targetId).insertAdjacentHTML('beforeend', sprintHTML);
        renumberSprints(targetId);
    }

    function generateSections(containerId, count) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        count = parseInt(count);
        if (isNaN(count) || count < 1) return;

        for (let i = 0; i < count; i++) {
            const sectionHTML = `
                <div class="row g-3 align-items-end mb-2 border-bottom pb-2">
                    <div class="col-md-6">
                        <label for="sectionName_${containerId}_${i}" class="form-label">Section ${i + 1} Name</label>
                        <input type="text" class="form-control" id="sectionName_${containerId}_${i}" name="sections[${containerId}][${i}][name]" placeholder="Enter section name">
                    </div>
                    <div class="col-md-6">
                        <label for="duration_${containerId}_${i}" class="form-label">Duration</label>
                        <input type="number" class="form-control" id="duration_${containerId}_${i}" name="sections[${containerId}][${i}][duration]" placeholder="Minutes">
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', sectionHTML);
        }
    }

    function removeElement(id) {
        const element = document.getElementById(id);
        if (element) {
            const isSprint = element.classList.contains('sprint-card');
            const parentSprintContainer = element.parentElement;

            element.remove();

            if (isSprint && parentSprintContainer) {
                renumberSprints(parentSprintContainer.id);
            } else {
                renumberMilestones();
            }
        }
    }

    function renumberMilestones() {
        const container = document.getElementById('milestoneContainer');
        const milestoneCards = container.querySelectorAll('.card.shadow-sm.border');

        milestoneCards.forEach((card, index) => {
            const heading = card.querySelector('h5');
            if (heading) {
                heading.textContent = `Milestone ${index + 1}`;
            }
        });
    }

    function renumberSprints(containerId) {
        const sprintContainer = document.getElementById(containerId);
        if (!sprintContainer) return;

        const sprintCards = sprintContainer.querySelectorAll('.sprint-card');

        sprintCards.forEach((card, index) => {
            const sprintTitle = card.querySelector('h6');
            if (sprintTitle) {
                sprintTitle.textContent = `Sprint ${index + 1}:`;
            }
        });
    }
    
    document.getElementById('courseForm').addEventListener('submit', function (e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        
        console.log(formData);
    
        // Get the selected course type
        const selectedType = document.querySelector('input[name="course_type"]:checked').value.trim();
    
        let typeMapped;
        if (selectedType === 'onlinerecurring') {
            typeMapped = 6;
        } else if (selectedType === 'onlinemilestone') {
            typeMapped = 7;
        } else {
            typeMapped = null; // Handle invalid cases
        }
    
        // Append course type, location, and other basic fields
        if (typeMapped !== null) {
            formData.append('course_type', typeMapped);
        }
        formData.append('location_id', document.getElementById('location_id').value.trim());
        formData.append('category_id', document.getElementById('category_id').value.trim());
        formData.append('name', document.getElementById('courseName').value.trim());
        formData.append('shortDescription', document.getElementById('shortDescription').value.trim());
        formData.append('description', document.getElementById('description').value.trim());
        formData.append('learningoutcome', document.getElementById('learningoutcome').value.trim());
        formData.append('age_min', document.getElementById('age_min').value.trim());
        formData.append('age_max', document.getElementById('age_max').value.trim());
        formData.append('start_date', document.getElementById('start_date').value.trim());
        formData.append('end_date', document.getElementById('end_date').value.trim());
        formData.append('price', document.getElementById('price').value.trim());
    
        // Handle course image
        const courseImage = document.getElementById('courseImage')?.files[0];
        if (courseImage) {
            formData.append('courseImage', courseImage);
        }
    
        // Handle Milestones
        const milestones = [];
        const milestoneCards = document.querySelectorAll('#milestoneContainer > .card');
        milestoneCards.forEach((card, mi) => {
            const milestoneData = {
                name: card.querySelector('input[type="text"]').value.trim(),
                description: card.querySelectorAll('textarea')[0].value.trim(),
                learningOutcome: card.querySelectorAll('textarea')[1].value.trim(),
                subjects: $(card).find('.select2').val() || [],
                startDate: card.querySelector('input[name="milestone_startDate[]"]')?.value || null,
                endDate: card.querySelector('input[name="milestone_endDate[]"]')?.value || null,
                price: card.querySelector('input[name="milestone_price[]"]')?.value || null,
                reductionPrice: card.querySelector('input[name="milestone_reductionPrice[]"]')?.value || null,
                sequence: mi + 1,
                sprints: []
            };
    
            // Handle milestone image
            const milestoneImageInput = card.querySelector('input[type="file"]');
            if (milestoneImageInput && milestoneImageInput.files[0]) {
                formData.append('milestoneImages[]', milestoneImageInput.files[0]);
            }
    
            // Handle sprints inside each milestone (if applicable)
            const sprintCards = card.querySelectorAll('.sprint-card');
            sprintCards.forEach((sprintCard, si) => {
                const sprint = {
                    theme: sprintCard.querySelector('input[placeholder="Theme Name"]').value.trim(),
                    sequence: si + 1,
                    sections: [],
                    description: sprintCard.querySelectorAll('textarea')[0].value.trim(),
                    learningOutcome: sprintCard.querySelectorAll('textarea')[1].value.trim(),
                    price: card.querySelector('input[name="sprintprice[]"]')?.value || null,
                    reductionPrice: card.querySelector('input[name="sprintreductionprice[]"]')?.value || null,
                    slots: card.querySelector('input[name="sprintslots[]"]')?.value || null,
                };
    
                const sprintImageInput = sprintCard.querySelector('input[type="file"]');
                if (sprintImageInput && sprintImageInput.files[0]) {
                    formData.append(`sprintImages[${mi}][${si}]`, sprintImageInput.files[0]);
                }
    
                const sectionRows = sprintCard.querySelectorAll('.row.g-3');
                sectionRows.forEach((sectionRow, idx) => {
                    sprint.sections.push({
                        name: sectionRow.querySelector(`input[id^="sectionName"]`).value.trim(),
                        duration: parseInt(sectionRow.querySelector(`input[id^="duration"]`).value || 0),
                        sequence: idx + 1
                    });
                });
    
                milestoneData.sprints.push(sprint);
            });
    
            milestones.push(milestoneData);
        });
    
        formData.append('milestones', JSON.stringify(milestones));
    
        // Handle Annual Package Fields
        // if (document.querySelector('input[name="package_type"]:checked').value === 'annual') {
            // const annualFields = {
            //     name: document.getElementById('annualName')?.value.trim(),
            //     startDates: document.querySelectorAll('input[name="annual_startDate[]"]'),
            //     endDates: document.querySelectorAll('input[name="annual_endDate[]"]'),
            //     reductions: document.querySelectorAll('input[name="annual_reduction[]"]'),
            //     levels: [],
            //     description: document.querySelector('textarea[name="annual_description"]')?.value.trim(),
            //     image: document.querySelector('input[name="annual_image"]')?.files[0],
            //     slots: document.querySelector('input[name="annual_slots"]')?.value.trim(),
            //     sessions: []
            // };
    
            // Append dynamic fields for the annual package
            // if (annualFields.name) formData.append('annual_name', annualFields.name);
            
            // Annual Dates and Reductions
            // annualFields.startDates.forEach((input, idx) => formData.append(`annual_startDate[${idx}]`, input.value.trim()));
            // annualFields.endDates.forEach((input, idx) => formData.append(`annual_endDate[${idx}]`, input.value.trim()));
            // annualFields.reductions.forEach((input, idx) => formData.append(`annual_reduction[${idx}]`, input.value.trim()));
    
            // Levels
            // const Alevels = [];
            // document.querySelectorAll('.level-item').forEach((row, idx) => {
            //     const nameInput = row.querySelector(`input[name="annual_levels[${idx}][name]"]`);
            //     const priceInput = row.querySelector(`input[name="annual_levels[${idx}][price]"]`);
            //     if (nameInput && priceInput) {
            //         Alevels.push({
            //             name: nameInput.value.trim(),
            //             price: priceInput.value.trim()
            //         });
            //     }
            // });
            // formData.append('annual_levels', JSON.stringify(Alevels));
    
            // Sessions
            // const AsessionSchedule = [];
            // document.querySelectorAll('.session-row').forEach((row, idx) => {
            //     const dayInput = row.querySelector(`select[name="annual_sessions[${idx}][day]"]`);
            //     const startTimeInput = row.querySelector(`input[name="annual_sessions[${idx}][start_time]"]`);
            //     const endTimeInput = row.querySelector(`input[name="annual_sessions[${idx}][end_time]"]`);
            //     if (dayInput && startTimeInput && endTimeInput) {
            //         AsessionSchedule.push({
            //             day: dayInput.value.trim(),
            //             start_time: startTimeInput.value.trim(),
            //             end_time: endTimeInput.value.trim()
            //         });
            //     }
            // });
            // formData.append('annual_sessions', JSON.stringify(AsessionSchedule));
    
            // Append additional fields like description, image, slots
            // if (annualFields.description) formData.append('annual_description', annualFields.description);
            // if (annualFields.image) formData.append('annual_image', annualFields.image);
            // if (annualFields.slots) formData.append('annual_slots', annualFields.slots);
        // }
    
        // Handle Quarterly Package Fields (similar to Annual)
        // You can follow a similar pattern for quarterly package fields if needed.
    
        // Handle Monthly Package Fields (similar to Annual)
    
        console.log(formData);
    
        // Submit the data
        fetch("{{ route('organizer.event.storeo') }}", {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
            },
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                iziToast.success({ title: 'Success', message: result.message });
                setTimeout(() => location.reload(), 1500);
            } else {
                iziToast.error({ title: 'Error', message: result.message });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            iziToast.error({ title: 'AJAX error', message: error.message });
        });
    });

    
    

    function saveCourse() {
        const formData = new FormData();

        // const selectedType = document.querySelector('input[name="course_type"]:checked').value.trim();
        // const typeMapped = selectedType === 'recurring' ? 4 : 3;
        // formData.append('course_type', typeMapped);
        
        const selectedType = document.querySelector('input[name="course_type"]:checked').value.trim();

        let typeMapped;
        
        if (selectedType === 'onlinerecurring') {
            typeMapped = 6;
        } else if (selectedType === 'onlinemilestone') {
            typeMapped = 7;
        } else {
            typeMapped = null; // or handle invalid cases
        }
        
        if (typeMapped !== null) {
            formData.append('course_type', typeMapped);
        }
        formData.append('location_id', document.getElementById('location_id').value.trim());
        formData.append('category_id', document.getElementById('category_id').value.trim());
        formData.append('name', document.getElementById('courseName').value.trim());
        formData.append('shortDescription', document.getElementById('shortDescription').value.trim());
        formData.append('description', document.getElementById('description').value.trim());
        formData.append('learningoutcome', document.getElementById('learningoutcome').value.trim());
        formData.append('age_min', document.getElementById('age_min').value.trim());
        formData.append('age_max', document.getElementById('age_max').value.trim());
        formData.append('start_date', document.getElementById('start_date').value.trim());
        formData.append('end_date', document.getElementById('end_date').value.trim());
        formData.append('price', document.getElementById('price').value.trim());

        const courseImage = document.getElementById('courseImage')?.files[0];
        if (courseImage) {
            formData.append('courseImage', courseImage);
        }

        const milestones = [];

        const milestoneCards = document.querySelectorAll('#milestoneContainer > .card');

        milestoneCards.forEach((card, mi) => {
            const milestoneData = {
                name: card.querySelector('input[type="text"]').value.trim(),
                description: card.querySelectorAll('textarea')[0].value.trim(),
                learningOutcome: card.querySelectorAll('textarea')[1].value.trim(),
                subjects: $(card).find('.select2').val() || [],
                sequence: mi + 1,
                sprints: []
            };

            //  Handle milestone image  always use [] notation
            const milestoneImageInput = card.querySelector('input[type="file"]');
            if (milestoneImageInput && milestoneImageInput.files[0]) {
                formData.append('milestoneImages[]', milestoneImageInput.files[0]);
            }

            // Handle sprints inside each milestone (if applicable)
            const sprintCards = card.querySelectorAll('.sprint-card');
            sprintCards.forEach((sprintCard, si) => {
                const sprint = {
                    theme: sprintCard.querySelector('input[placeholder="Theme Name"]').value.trim(),
                    sequence: si + 1,
                    sections: [],
                    description: sprintCard.querySelectorAll('textarea')[0].value.trim(),
                    learningOutcome: sprintCard.querySelectorAll('textarea')[1].value.trim(),
                };

                const sprintImageInput = sprintCard.querySelector('input[type="file"]');
                if (sprintImageInput && sprintImageInput.files[0]) {
                    formData.append(`sprintImages[${mi}][${si}]`, sprintImageInput.files[0]);
                }

                const sectionRows = sprintCard.querySelectorAll('.row.g-3');
                sectionRows.forEach((sectionRow, idx) => {
                    sprint.sections.push({
                        name: sectionRow.querySelector(`input[id^="sectionName"]`).value.trim(),
                        duration: parseInt(sectionRow.querySelector(`input[id^="duration"]`).value || 0),
                        sequence: idx + 1
                    });
                });

                milestoneData.sprints.push(sprint);
            });

            milestones.push(milestoneData);
        });

        // Other fields here...
        formData.append('milestones', JSON.stringify(milestones));
        
        console.log(formData);

        axios.post("{{ route('organizer.event.storeo') }}", formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(res => {
            iziToast.success({ title: 'Success', message: res.data.message });
            // setTimeout(() => location.reload(), 1500);
            
            // Show the after-save div
            document.getElementById('assignmentSection').style.display = 'block';
        
            // Optionally scroll to it
            document.getElementById('assignmentSection').scrollIntoView({ behavior: 'smooth' });
        
            // Or if needed, clear the form
            // document.querySelector('form').reset();  // if wrapped in <form>
            
        })
        .catch(err => {
            const message = err.response?.data?.message || 'Something went wrong';
            iziToast.error({ title: 'Error', message });
        });
    }
    
    function saveCourselst() {
    const formData = new FormData();

    // Course Type Mapping
    const selectedType = document.querySelector('input[name="course_type"]:checked').value.trim();
    let typeMapped = null;

    if (selectedType === 'onlinerecurring') typeMapped = 6;
    else if (selectedType === 'onlinemilestone') typeMapped = 7;

    if (typeMapped !== null) formData.append('course_type', typeMapped);

    // Static Fields
    formData.append('location_id', document.getElementById('location_id').value.trim());
    formData.append('category_id', document.getElementById('category_id').value.trim());
    formData.append('name', document.getElementById('courseName').value.trim());
    formData.append('shortDescription', document.getElementById('shortDescription').value.trim());
    formData.append('description', document.getElementById('description').value.trim());
    formData.append('learningoutcome', document.getElementById('learningoutcome').value.trim());
    formData.append('age_min', document.getElementById('age_min').value.trim());
    formData.append('age_max', document.getElementById('age_max').value.trim());
    formData.append('start_date', document.getElementById('start_date').value.trim());
    formData.append('end_date', document.getElementById('end_date').value.trim());
    formData.append('price', document.getElementById('price').value.trim());

    const courseImage = document.getElementById('courseImage')?.files[0];
    if (courseImage) {
        formData.append('courseImage', courseImage);
    }

    // ========== MILESTONES ==========
    const milestones = [];
    const milestoneCards = document.querySelectorAll('#milestoneContainer > .card');

    milestoneCards.forEach((card, mi) => {
        const milestoneData = {
            name: card.querySelector('input[type="text"]').value.trim(),
            description: card.querySelectorAll('textarea')[0].value.trim(),
            learningOutcome: card.querySelectorAll('textarea')[1].value.trim(),
            subjects: $(card).find('.select2').val() || [],
            sequence: mi + 1,
            sprints: []
        };

        const milestoneImageInput = card.querySelector('input[type="file"]');
        if (milestoneImageInput && milestoneImageInput.files[0]) {
            formData.append('milestoneImages[]', milestoneImageInput.files[0]);
        }

        // SPRINTS
        const sprintCards = card.querySelectorAll('.sprint-card');
        sprintCards.forEach((sprintCard, si) => {
            const sprint = {
                theme: sprintCard.querySelector('input[placeholder="Theme Name"]').value.trim(),
                sequence: si + 1,
                sections: [],
                description: sprintCard.querySelectorAll('textarea')[0].value.trim(),
                learningOutcome: sprintCard.querySelectorAll('textarea')[1].value.trim()
            };

            const sprintImageInput = sprintCard.querySelector('input[type="file"]');
            if (sprintImageInput && sprintImageInput.files[0]) {
                formData.append(`sprintImages[${mi}][${si}]`, sprintImageInput.files[0]);
            }

            const sectionRows = sprintCard.querySelectorAll('.row.g-3');
            sectionRows.forEach((sectionRow, idx) => {
                sprint.sections.push({
                    name: sectionRow.querySelector(`input[id^="sectionName"]`)?.value.trim(),
                    duration: parseInt(sectionRow.querySelector(`input[id^="duration"]`)?.value || 0),
                    sequence: idx + 1
                });
            });

            milestoneData.sprints.push(sprint);
        });

        milestones.push(milestoneData);
    });

    formData.append('milestones', JSON.stringify(milestones));

    // ========== QUARTERLY PACKAGE ==========
    const quarterlyPackages = [];
    const names = document.querySelectorAll('input[name="quarterly_name[]"]');
    const starts = document.querySelectorAll('input[name="quarterly_start_date[]"]');
    const ends = document.querySelectorAll('input[name="quarterly_end_date[]"]');

    for (let i = 0; i < names.length; i++) {
        quarterlyPackages.push({
            name: names[i].value.trim(),
            start_date: starts[i].value,
            end_date: ends[i].value
        });
    }

    formData.append('quarterlyPackages', JSON.stringify(quarterlyPackages));

    // ========== LEVELS FOR EACH PACKAGE ==========
    ['annual', 'quarterly', 'monthly'].forEach(type => {
        const levelContainer = document.getElementById(`${type}LevelsContainer`);
        if (!levelContainer) return;

        const rows = levelContainer.querySelectorAll('.level-item');
        const levels = [];

        rows.forEach((row, index) => {
            const name = row.querySelector('input[name^="' + type + '_levels"]')?.value.trim();
            const price = row.querySelector('input[type="number"]')?.value;
            levels.push({ name, price, sequence: index + 1 });
        });

        formData.append(`${type}Levels`, JSON.stringify(levels));
    });

    // ========== SESSION SCHEDULES FOR EACH PACKAGE ==========
    ['annual', 'quarterly', 'monthly'].forEach(type => {
        const sessionContainer = document.getElementById(`sessionSchedule_1to1_${type}`);
        if (!sessionContainer) return;

        const sessionRows = sessionContainer.querySelectorAll('.session-row');
        const sessions = [];

        sessionRows.forEach((row, index) => {
            const day = row.querySelector('select')?.value;
            const start = row.querySelector('input[type="time"]:nth-of-type(1)')?.value;
            const end = row.querySelector('input[type="time"]:nth-of-type(2)')?.value;

            sessions.push({ day, start_time: start, end_time: end, sequence: index + 1 });
        });

        formData.append(`${type}Sessions`, JSON.stringify(sessions));
    });

    // ========== Submit ==========
    axios.post("{{ route('organizer.event.storeo') }}", formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(res => {
        iziToast.success({ title: 'Success', message: res.data.message });
        // Optional: location.reload();
    })
    .catch(err => {
        const message = err.response?.data?.message || 'Something went wrong';
        iziToast.error({ title: 'Error', message });
    });
}

document.getElementById('test').addEventListener('submit', function (e) {
    e.preventDefault();

    const form = e.target;
    const formData = new FormData(form);
    
            // const selectedType = document.querySelector('input[name="course_type"]:checked').value.trim();
            // const typeMapped = selectedType === 'recurring' ? 4 : 3;
            // formData.append('course_type', typeMapped);
            
            const selectedType = document.querySelector('input[name="course_type"]:checked').value.trim();
    
            let typeMapped;
            
            if (selectedType === 'onlinerecurring') {
                typeMapped = 6;
            } else if (selectedType === 'onlinemilestone') {
                typeMapped = 7;
            } else {
                typeMapped = null; // or handle invalid cases
            }
            
            if (typeMapped !== null) {
                formData.append('course_type', typeMapped);
            }
            formData.append('location_id', document.getElementById('location_id').value.trim());
            formData.append('category_id', document.getElementById('category_id').value.trim());
            formData.append('name', document.getElementById('courseName').value.trim());
            formData.append('shortDescription', document.getElementById('shortDescription').value.trim());
            formData.append('description', document.getElementById('description').value.trim());
            formData.append('learningoutcome', document.getElementById('learningoutcome').value.trim());
            formData.append('age_min', document.getElementById('age_min').value.trim());
            formData.append('age_max', document.getElementById('age_max').value.trim());
            formData.append('start_date', document.getElementById('start_date').value.trim());
            formData.append('end_date', document.getElementById('end_date').value.trim());
            formData.append('price', document.getElementById('price').value.trim());
    
            const courseImage = document.getElementById('courseImage')?.files[0];
            if (courseImage) {
                formData.append('courseImage', courseImage);
            }
    
            const milestones = [];
    
            const milestoneCards = document.querySelectorAll('#milestoneContainer > .card');
    
            milestoneCards.forEach((card, mi) => {
                const milestoneData = {
                    name: card.querySelector('input[type="text"]').value.trim(),
                    description: card.querySelectorAll('textarea')[0].value.trim(),
                    learningOutcome: card.querySelectorAll('textarea')[1].value.trim(),
                    subjects: $(card).find('.select2').val() || [],
                    sequence: mi + 1,
                    sprints: []
                };
    
                //  Handle milestone image  always use [] notation
                const milestoneImageInput = card.querySelector('input[type="file"]');
                if (milestoneImageInput && milestoneImageInput.files[0]) {
                    formData.append('milestoneImages[]', milestoneImageInput.files[0]);
                }
    
                // Handle sprints inside each milestone (if applicable)
                const sprintCards = card.querySelectorAll('.sprint-card');
                sprintCards.forEach((sprintCard, si) => {
                    const sprint = {
                        theme: sprintCard.querySelector('input[placeholder="Theme Name"]').value.trim(),
                        sequence: si + 1,
                        sections: [],
                        description: sprintCard.querySelectorAll('textarea')[0].value.trim(),
                        learningOutcome: sprintCard.querySelectorAll('textarea')[1].value.trim(),
                    };
    
                    const sprintImageInput = sprintCard.querySelector('input[type="file"]');
                    if (sprintImageInput && sprintImageInput.files[0]) {
                        formData.append(`sprintImages[${mi}][${si}]`, sprintImageInput.files[0]);
                    }
    
                    const sectionRows = sprintCard.querySelectorAll('.row.g-3');
                    sectionRows.forEach((sectionRow, idx) => {
                        sprint.sections.push({
                            name: sectionRow.querySelector(`input[id^="sectionName"]`).value.trim(),
                            duration: parseInt(sectionRow.querySelector(`input[id^="duration"]`).value || 0),
                            sequence: idx + 1
                        });
                    });
    
                    milestoneData.sprints.push(sprint);
                });
    
                milestones.push(milestoneData);
            });
    
            // Other fields here...
            formData.append('milestones', JSON.stringify(milestones));
            
            console.log(formData);
    
            axios.post("{{ route('organizer.event.storeo') }}", formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(res => {
                iziToast.success({ title: 'Success', message: res.data.message });
                setTimeout(() => location.reload(), 1500);
                
                // Show the after-save div
                //document.getElementById('assignmentSection').style.display = 'block';
            
                // Optionally scroll to it
                //document.getElementById('assignmentSection').scrollIntoView({ behavior: 'smooth' });
            
                // Or if needed, clear the form
                // document.querySelector('form').reset();  // if wrapped in <form>
                
            })
            .catch(err => {
                const message = err.response?.data?.message || 'Something went wrong';
                iziToast.error({ title: 'Error', message });
            });
        });




</script>
@endpush
