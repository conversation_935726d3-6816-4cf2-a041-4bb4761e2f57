@extends($activeTemplate . 'organizer.layouts.master')
@section('content')
<div class="row">
    <div class="col-lg-12">
    <form id="courseEditForm">
                    @csrf
                    <input type="hidden" name="course_id" value="{{ $course->id }}">
                    <input type="hidden" name="old_course_image" value="{{ $course->cover_image }}">

        <!-- Course Info -->
        <div class="card shadow-sm border-0 mb-4">
        
            <div class="card-header">
                <h5 class="">{{ __($pageTitle) }}</h5>
            </div>
            <div class="card-body">
            
                <!-- Course Type Radio Buttons -->
                <div class="mb-3">
                    <label class="form-label">Course Type</label>
                    <div>
                        <div class="form-check form-check-inline">
                        <!-- Online Recurring = coursetype = 6 Online Milestone = coursetype = 7 -->
                            <input class="form-check-input" type="radio" name="course_type" id="course_type_recurring" value="onlinerecurring" {{ $course->type == 6 ? 'checked' : '' }}>
                            <label class="form-check-label" for="course_type_recurring">Online Recurring</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="course_type" id="course_type_milestone" value="onlinemilestone" {{ $course->type == 7 ? 'checked' : '' }}>
                            <label class="form-check-label" for="course_type_milestone">Online Milestone</label>
                        </div>
                    </div>
                </div>
                <!-- 1 to 1 / 1 to many toggle section -->
                <div id="recurringOptions" style="display: {{ $course->type == 6 ? 'block' : 'none' }};" class="mb-3">
                    <label class="form-label">Recurring Type</label>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="recurring_type" id="one_to_one" value="1to1" {{ $course->online_type == '1to1' ? 'checked' : '' }}>
                            <label class="form-check-label" for="one_to_one">1 to 1</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="recurring_type" id="one_to_many" value="1tomany" {{ $course->online_type == '1tomany' ? 'checked' : '' }}>
                            <label class="form-check-label" for="one_to_many">1 to many</label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <!-- Category Dropdown -->
                    <div class="col-md-6 mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select name="category_id" id="category_id" class="form-select" required>
                            <option value="">Select Category</option>
                            @foreach($categories as $cat)
                            <option value="{{ $cat->id }}" {{ $course->category_id == $cat->id ? 'selected' : '' }}>{{ $cat->name }}</option>
                            @endforeach
                        
                        </select>
                    </div>

                    <!-- Location Dropdown -->
                    <div class="col-md-6 mb-3">
                        <label for="location" class="form-label">Location</label>
                        <select name="location_id" id="location_id" class="form-select" required>
                            <option value="">Select Location</option>
                            @foreach($locations as $loc)
                            <option value="{{ $loc->id }}" {{ $course->location_id == $loc->id ? 'selected' : '' }}>{{ $loc->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="courseName" class="form-label">Course Name</label>
                        <input type="text" class="form-control" id="courseName" name="courseName" placeholder="Enter course name" value="{{ $course->title }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="shortDescription" class="form-label">Short Description</label>
                        <textarea class="form-control" id="shortDescription" name="shortDescription" placeholder="Short description">{{ $course->short_description }}</textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2" placeholder="Description">{{ $course->description }}</textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="learningoutcome" class="form-label">Learning Outcome</label>
                        <textarea class="form-control" id="learningoutcome" name="learningoutcome" rows="2" placeholder="Learning outcome">{{ $course->learningoutcome }}</textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="min_age" class="form-label">Min Age</label>
                        <input type="number" name="min_age" id="age_min" class="form-control" required value="{{ $course->age_min }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="max_age" class="form-label">Max Age</label>
                        <input type="number" name="max_age" id="age_max" class="form-control" required value="{{ $course->age_max }}">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="price" class="form-label">Price</label>
                        <input type="number" class="form-control" id="price" name="price" placeholder="Enter price" value="{{ $course->price }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="seats" class="form-label">Total Seats</label>
                        <input type="number" name="seats" id="seats" class="form-control" placeholder="Enter seats" value="{{ $course->seats }}">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="courseImage" class="form-label">Course Image</label>
                    <input type="file" class="form-control" id="courseImage" name="courseImage">
                    @if($course->cover_image)
                        <div class="mt-2">
                            <img src="{{ getImage(getFilePath('eventCover') . '/' . $course->cover_image, getFileSize('eventCover')) }}" alt="Current Image" style="max-width: 200px; height: auto;">
                            <p class="text-muted">Current image</p>
                        </div>
                    @endif
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" name="start_date" id="start_date" class="form-control" required value="{{ $course->start_date }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" name="end_date" id="end_date" class="form-control" required value="{{ $course->end_date }}">
                    </div>
                </div>
                <div id="packageFields" style="display: {{ $course->type == 6 ? 'block' : 'none' }};">
                    <!-- Annual package-->
                    <div class="AnnualpackageList">
                        <h6>Annual package</h6>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="annualName" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="annualName" name="annual_name" placeholder="Name" value="{{ isset($packages) && isset($packages->annual_details['name']) ? $packages->annual_details['name'] : '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" name="annual_startDate[]" value="{{ isset($packages) && isset($packages->annual_details['start_date']) ? $packages->annual_details['start_date'] : '' }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control" name="annual_endDate[]" value="{{ isset($packages) && isset($packages->annual_details['end_date']) ? $packages->annual_details['end_date'] : '' }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Reduction Price (₹)</label>
                                <input type="number" class="form-control" name="annual_reduction[]" placeholder="Price" value="{{ isset($packages) && isset($packages->annual_details['reduction_price']) ? $packages->annual_details['reduction_price'] : '' }}">
                            </div>
                            <div class="row">
                                <h6>Level</h6>
                                <div id="annualLevelsContainer">
                                    @if(isset($packages->annual_details['levels']) && is_array($packages->annual_details['levels']))
                                        @foreach($packages->annual_details['levels'] as $index => $level)
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="annual_levels[{{ $index }}][name]" placeholder="Level name" value="{{ $level['name'] ?? '' }}">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="annual_levels[{{ $index }}][description]" rows="2" placeholder="Description">{{ $level['description'] ?? '' }}</textarea>
                                            </div>

                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="annual_levels[{{ $index }}][price]" placeholder="Price" value="{{ $level['price'] ?? '' }}">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                @if($index > 0)
                                                <button type="button" class="btn btn-danger btn-sm" onclick="removeLevelRow(this)">Remove</button>
                                                @endif
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="annual_levels[0][name]" placeholder="Level name">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="annual_levels[0][description]" rows="2" placeholder="Description"></textarea>
                                            </div>

                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="annual_levels[0][price]" placeholder="Price">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                <!-- Remove button shown only on dynamically added rows -->
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <div class="mb-3">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addLevelRow('annual')">+ Add More</button>
                                </div>

                                <!-- Common fields for all levels -->
                                <div class="row">

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" class="form-control" name="annual_image">
                                        @if(isset($packages->annual_details['image']) && $packages->annual_details['image'])
                                            <div class="mt-2">
                                                <img src="{{ getImage(getFilePath('eventCover') . '/' . $packages->annual_details['image'], getFileSize('eventCover')) }}" alt="Annual Package Image" style="max-width: 100px; height: auto;">
                                                <p class="text-muted">Current image</p>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Max. Slots/Week</label>
                                        <input type="number" class="form-control" name="annual_slots" placeholder="" value="{{ isset($packages->annual_details['max_slots']) ? $packages->annual_details['max_slots'] : '' }}">
                                    </div>
                                </div>
                            </div>


                            <div class="form-group mt-4" id="sessionSchedule_1to1_annual" style="display: {{ $course->online_type == '1to1' ? 'block' : 'none' }};">
                                <label>Session Schedule</label>
                                <div >
                                    @if(isset($packages->annual_details['sessions']) && is_array($packages->annual_details['sessions']))
                                        @foreach($packages->annual_details['sessions'] as $index => $session)
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-3">
                                                <label>Day</label>
                                                <select class="form-select" name="annual_sessions[{{ $index }}][day]">
                                                    <option value="">Select Day</option>
                                                    @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                        <option value="{{ $day }}" {{ isset($session['day']) && $session['day'] == $day ? 'selected' : '' }}>{{ $day }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label>Start Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[{{ $index }}][start_time]" value="{{ $session['start_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-3">
                                                <label>End Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[{{ $index }}][end_time]" value="{{ $session['end_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <!-- Default first row -->
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-3">
                                                <label>Day</label>
                                                <select class="form-select" name="annual_sessions[0][day]">
                                                    <option value="">Select Day</option>
                                                    @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                        <option value="{{ $day }}">{{ $day }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label>Start Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[0][start_time]" />
                                            </div>
                                            <div class="col-md-3">
                                                <label>End Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[0][end_time]" />
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRow('annual')">+ Add More</button>
                            </div>

                            <div class="form-group mt-4" id="sessionSchedule_1tomany_annual" style="display: {{ $course->online_type == '1tomany' ? 'block' : 'none' }};">
                                <label>Session Schedule</label>
                                <div >
                                    @if(isset($packages->annual_details['sessions']) && is_array($packages->annual_details['sessions']))
                                        @foreach($packages->annual_details['sessions'] as $index => $session)
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-12 mb-2">
                                                <label class="form-label">Slot name</label>
                                                <input type="text" class="form-control" name="annual_sessions[{{ $index }}][slotname]" placeholder="Slot name" value="{{ $session['slotname'] ?? '' }}">
                                            </div>
                                            <div class="col-md-12">
                                                <label class="form-label">Day</label>
                                                    @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="checkbox" name="annual_sessions[{{ $index }}][day][]" value="{{ $day }}" {{ isset($session['day']) && is_array($session['day']) && in_array($day, $session['day']) ? 'checked' : '' }}> {{ $day }}
                                                        </div>
                                                    @endforeach
                                            </div>
                                            <div class="col-md-5">
                                                <label class="form-label">Start Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[{{ $index }}][start_time]" value="{{ $session['start_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-5">
                                                <label class="form-label">End Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[{{ $index }}][end_time]" value="{{ $session['end_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <!-- Default first row -->
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-12 mb-2">
                                                <label class="form-label">Slot name</label>
                                                <input type="text" class="form-control" name="annual_sessions[0][slotname]" placeholder="Slot name">
                                            </div>
                                            <div class="col-md-12">
                                                <label class="form-label">Day</label>
                                                    @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="checkbox" name="annual_sessions[0][day][]" value="{{ $day }}"> {{ $day }}
                                                        </div>
                                                    @endforeach
                                            </div>
                                            <div class="col-md-5">
                                                <label class="form-label">Start Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[0][start_time]" />
                                            </div>
                                            <div class="col-md-5">
                                                <label class="form-label">End Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[0][end_time]" />
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRowM('annual')">+ Add More</button>
                            </div>


                        </div>
                    </div>
                    <!-- Quarterly package-->
                    <div class="QuarterlypackageList">
                        <h6>Quarterly package</h6>

                        <div id="quarterlyPackageWrapper">
                            @if(isset($packages->quarterly_details['packages']) && is_array($packages->quarterly_details['packages']))
                                @foreach($packages->quarterly_details['packages'] as $index => $package)
                                <div class="quarterly-package-item row g-3 {{ $index > 0 ? 'mt-2' : '' }}">
                                    <div class="col-md-3">
                                        <label>Name</label>
                                        <input type="text" class="form-control" name="quarterly_name[]" placeholder="Name" value="{{ $package['name'] ?? '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label>Start Date</label>
                                        <input type="date" class="form-control" name="quarterly_start_date[]" value="{{ $package['start_date'] ?? '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label>End Date</label>
                                        <input type="date" class="form-control" name="quarterly_end_date[]" value="{{ $package['end_date'] ?? '' }}">
                                    </div>
                                    <div class="col-md-3 d-flex align-items-end">
                                        @if($index > 0)
                                        <button type="button" class="btn btn-danger btn-sm removeQuarterly">X</button>
                                        @endif
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="quarterly-package-item row g-3">
                                    <div class="col-md-3">
                                        <label>Name</label>
                                        <input type="text" class="form-control" name="quarterly_name[]" placeholder="Name" >
                                    </div>
                                    <div class="col-md-3">
                                        <label>Start Date</label>
                                        <input type="date" class="form-control" name="quarterly_start_date[]" >
                                    </div>
                                    <div class="col-md-3">
                                        <label>End Date</label>
                                        <input type="date" class="form-control" name="quarterly_end_date[]" >
                                    </div>
                                    <div class="col-md-3 d-flex align-items-end">
                                        <button type="button" class="btn btn-danger btn-sm removeQuarterly">X</button>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-primary" id="addQuarterly">+ Add More</button>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Reduction Price (₹)</label>
                                <input type="number" class="form-control" name="quarterly_reduction[]" placeholder="Price" value="{{ isset($packages->quarterly_details['reduction_price']) ? $packages->quarterly_details['reduction_price'] : '' }}">
                            </div>

                            <div class="row">
                                <h6>Level</h6>
                                <div id="quarterlyLevelsContainer">
                                    @if(isset($packages->quarterly_details['levels']) && is_array($packages->quarterly_details['levels']))
                                        @foreach($packages->quarterly_details['levels'] as $index => $level)
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="quarterly_levels[{{ $index }}][name]" placeholder="Level name" value="{{ $level['name'] ?? '' }}">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="quarterly_levels[{{ $index }}][description]" rows="2" placeholder="Description">{{ $level['description'] ?? '' }}</textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="quarterly_levels[{{ $index }}][price]" placeholder="Price" value="{{ $level['price'] ?? '' }}">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                @if($index > 0)
                                                <button type="button" class="btn btn-danger btn-sm" onclick="removeLevelRow(this)">Remove</button>
                                                @endif
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="quarterly_levels[0][name]" placeholder="Level name">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="quarterly_levels[0][description]" rows="2" placeholder="Description"></textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="quarterly_levels[0][price]" placeholder="Price">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                <!-- Remove button shown only on dynamically added rows -->
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <div class="mb-3">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addLevelRow('quarterly')">+ Add More</button>
                                </div>

                                <!-- Common fields for all levels -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" class="form-control" name="quarterly_image">
                                        @if(isset($packages->quarterly_details['image']) && $packages->quarterly_details['image'])
                                            <div class="mt-2">
                                                <img src="{{ getImage(getFilePath('eventCover') . '/' . $packages->quarterly_details['image'], getFileSize('eventCover')) }}" alt="Quarterly Package Image" style="max-width: 100px; height: auto;">
                                                <p class="text-muted">Current image</p>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Max. Slots/Week</label>
                                        <input type="number" class="form-control" name="quarterly_slots" placeholder="" value="{{ isset($packages->quarterly_details['max_slots']) ? $packages->quarterly_details['max_slots'] : '' }}">
                                    </div>
                                </div>
                            </div>

                            <!-- Add quarterly session schedules similar to annual -->
                            <div class="form-group mt-4" id="sessionSchedule_1to1_quarterly" style="display: {{ $course->online_type == '1to1' ? 'block' : 'none' }};">
                                <label>Session Schedule</label>
                                <div >
                                    <!-- First row -->
                                    <div class="row align-items-center mb-2 session-row">
                                        <div class="col-md-3">
                                            <label>Day</label>
                                            <select class="form-select" name="quarterly_sessions[0][day]">
                                                <option value="">Select Day</option>
                                                @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                    <option value="{{ $day }}">{{ $day }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label>Start Time</label>
                                            <input type="time" class="form-control" name="quarterly_sessions[0][start_time]" />
                                        </div>
                                        <div class="col-md-3">
                                            <label>End Time</label>
                                            <input type="time" class="form-control" name="quarterly_sessions[0][end_time]" />
                                        </div>
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRow('quarterly')">+ Add More</button>
                            </div>

                            <div class="form-group mt-4" id="sessionSchedule_1tomany_quarterly" style="display: {{ $course->online_type == '1tomany' ? 'block' : 'none' }};">
                                <label>Session Schedule</label>
                                <div >
                                    <!-- First row -->
                                    <div class="row align-items-center mb-2 session-row">

                                        <div class="col-md-12 mb-2">
                                            <label class="form-label">Slot name</label>
                                            <input type="text" class="form-control" name="quarterly_sessions[0][slotname]" placeholder="Slot name">
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Day</label>
                                                @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" name="quarterly_sessions[0][day][]" value="{{ $day }}"> {{ $day }}
                                                    </div>
                                                @endforeach
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">Start Time</label>
                                            <input type="time" class="form-control" name="quarterly_sessions[0][start_time]" >
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">End Time</label>
                                            <input type="time" class="form-control" name="quarterly_sessions[0][end_time]" />
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addSessionRowM('quarterly')">+ Add More</button>
                            </div>

                        </div>
                    </div>
                    <!-- Monthly package - similar structure -->
                    <div class="MonthlypackageList">
                        <h6>Monthly package</h6>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="monthlyName" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="monthlyName" name="monthly_name" placeholder="Name" value="{{ isset($packages->monthly_details['name']) ? $packages->monthly_details['name'] : '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" name="monthly_startDate[]" value="{{ isset($packages->monthly_details['start_date']) ? $packages->monthly_details['start_date'] : '' }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control" name="monthly_endDate[]" value="{{ isset($packages->monthly_details['end_date']) ? $packages->monthly_details['end_date'] : '' }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Reduction Price (₹)</label>
                                <input type="number" class="form-control" name="monthly_reduction[]" placeholder="Price" value="{{ isset($packages->monthly_details['reduction_price']) ? $packages->monthly_details['reduction_price'] : '' }}">
                            </div>

                            <!-- Monthly levels and sessions similar to annual/quarterly -->

                        </div>
                    </div>
                </div>

            </div>
        </div>
        <!-- Milestone Container -->
        <div id="milestoneSection" style="display: {{ $course->type == 7 ? 'block' : 'none' }};">
            <!-- Milestone Container -->
            <div id="milestoneContainer">
                @if(isset($milestones) && count($milestones) > 0)
                    @foreach($milestones as $index => $milestone)
                    <div class="card shadow-sm border mb-4" id="milestone_{{ $milestone->id }}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0 text-primary">Milestone {{ $index + 1 }}</h5>
                                <button class="btn btn-sm btn-danger" onclick="removeElement('milestone_{{ $milestone->id }}')">Remove</button>
                            </div>
                            <div class="row">
                                <div class="form-group col-md-12">
                                    <label for="milestoneName_{{ $milestone->id }}" class="form-label">Milestone Name</label>
                                    <input type="text" class="form-control mb-2" id="milestoneName_{{ $milestone->id }}" name="milestoneName[]" placeholder="Milestone Name" value="{{ $milestone->name }}">
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="milestoneDesc_{{ $milestone->id }}" class="form-label">Milestone Description</label>
                                    <textarea class="form-control mb-2" id="milestoneDesc_{{ $milestone->id }}" name="milestoneDesc[]" placeholder="Milestone Description">{{ $milestone->description }}</textarea>
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="learningOutcome_{{ $milestone->id }}" class="form-label">Learning Outcome</label>
                                    <textarea class="form-control mb-3" id="learningOutcome_{{ $milestone->id }}" name="learningOutcome[]" placeholder="Learning Outcome">{{ $milestone->learningoutcome }}</textarea>
                                </div>
                                <div class="form-group col-md-6">
                                    <label class="form-label">Image</label>
                                    <input type="file" class="form-control mb-3" name="milestoneImage[]">
                                    @if($milestone->milestoneImages)
                                        <div class="mt-2">
                                            <img src="{{ getImage(getFilePath('eventCover') . '/' . $milestone->milestoneImages, getFileSize('eventCover')) }}" alt="Milestone Image" style="max-width: 100px; height: auto;">
                                            <p class="text-muted">Current image</p>
                                        </div>
                                    @endif
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="subjects_{{ $milestone->id }}" class="form-label">Subjects</label>
                                    <select class="form-control select2 mb-3" id="subjects_{{ $milestone->id }}" name="subjects[{{ $milestone->id }}][]" multiple>
                                        <option value="Communication" {{ in_array('Communication', json_decode($milestone->subjects ?? '[]', true)) ? 'selected' : '' }}>Communication</option>
                                        <option value="Arts" {{ in_array('Arts', json_decode($milestone->subjects ?? '[]', true)) ? 'selected' : '' }}>Arts</option>
                                        <option value="Maths" {{ in_array('Maths', json_decode($milestone->subjects ?? '[]', true)) ? 'selected' : '' }}>Maths</option>
                                        <option value="PD" {{ in_array('PD', json_decode($milestone->subjects ?? '[]', true)) ? 'selected' : '' }}>PD</option>
                                        <option value="STEM" {{ in_array('STEM', json_decode($milestone->subjects ?? '[]', true)) ? 'selected' : '' }}>STEM</option>
                                    </select>
                                </div>

                                <div class="form-group col-md-3">
                                    <label>Start Date</label>
                                    <input type="date" name="milestone_startDate[]" class="form-control" value="{{ $milestone->start_date }}" />
                                </div>
                                <div class="form-group col-md-3">
                                    <label>End Date</label>
                                    <input type="date" name="milestone_endDate[]" class="form-control" value="{{ $milestone->end_date }}" />
                                </div>
                                <div class="form-group col-md-3">
                                    <label>Milestone Price</label>
                                    <input type="number" name="milestone_price[]" class="form-control" value="{{ $milestone->price }}" />
                                </div>
                                <div class="form-group col-md-3">
                                    <label>Milestone Reduction Price</label>
                                    <input type="number" name="milestone_reduction_price[]" class="form-control" value="{{ $milestone->reduction_price }}" />
                                </div>
                            </div>

                            <!-- Sprints for this milestone -->
                            <div class="mt-4">
                                <h6>Sprints</h6>
                                <div id="sprintContainer_{{ $milestone->id }}">
                                    <!-- Existing sprints will be loaded here via JavaScript -->
                                </div>
                                <button type="button" class="btn btn-success btn-sm" onclick="addSprint('{{ $milestone->id }}')">+ Add Sprint</button>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @endif
            </div>
            <button type="button" class="btn btn-success mb-3" onclick="addMilestone()">+ Add Milestone</button>
        </div>

        <div class="mt-4" style="float:right">
            <button type="submit" class="btn btn-primary mt-3">Update Course</button>
        </div>
        </form>

    </div>
</div>

<x-confirmation-modal />
@endsection

@push('breadcrumb-plugins')
    <x-search-form placeholder="Name | Organizer" />
@endpush

@push('script')
<!-- Include Axios from CDN -->
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const typeRadios = document.querySelectorAll('input[name="course_type"]');
        const milestoneSection = document.getElementById('milestoneSection');
        const packageFields = document.getElementById('packageFields');
        const recurringOptions = document.getElementById('recurringOptions');

        function handleCourseTypeChange(value) {
            if (value === 'onlinemilestone') {
                milestoneSection.style.display = 'block';
                packageFields.style.display = 'none';
                recurringOptions.style.display = 'none';
            } else if (value === 'onlinerecurring') {

                milestoneSection.style.display = 'none';
                packageFields.style.display = 'block';
                recurringOptions.style.display = 'block'; // Show the new toggle options
            } else {
                milestoneSection.style.display = 'none';
                packageFields.style.display = 'block';
                recurringOptions.style.display = 'none';
            }
        }

        // Attach listener to all radios
        typeRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                handleCourseTypeChange(this.value);
            });
        });

        // Trigger logic once on load using the checked radio
        const selectedRadio = document.querySelector('input[name="course_type"]:checked');
        if (selectedRadio) {
            handleCourseTypeChange(selectedRadio.value);
        }
    });

    document.addEventListener('DOMContentLoaded', function () {
        const recurringRadios = document.querySelectorAll('input[name="recurring_type"]');

        const div1to1_annual = document.getElementById('sessionSchedule_1to1_annual');
        const div1toMany_annual = document.getElementById('sessionSchedule_1tomany_annual');

        const div1to1_quarterly = document.getElementById('sessionSchedule_1to1_quarterly');
        const div1toMany_quarterly = document.getElementById('sessionSchedule_1tomany_quarterly');

        const div1to1_monthly = document.getElementById('sessionSchedule_1to1_monthly');
        const div1toMany_monthly = document.getElementById('sessionSchedule_1tomany_monthly');

        function handleRecurringChange(value)
        {
            if (value === '1to1') {
                if(div1to1_annual) div1to1_annual.style.display = 'block';
                if(div1toMany_annual) div1toMany_annual.style.display = 'none';

                if(div1to1_quarterly) div1to1_quarterly.style.display = 'block';
                if(div1toMany_quarterly) div1toMany_quarterly.style.display = 'none';

                if(div1to1_monthly) div1to1_monthly.style.display = 'block';
                if(div1toMany_monthly) div1toMany_monthly.style.display = 'none';

            } else if (value === '1tomany') {
                if(div1to1_annual) div1to1_annual.style.display = 'none';
                if(div1toMany_annual) div1toMany_annual.style.display = 'block';

                if(div1to1_quarterly) div1to1_quarterly.style.display = 'none';
                if(div1toMany_quarterly) div1toMany_quarterly.style.display = 'block';

                if(div1to1_monthly) div1to1_monthly.style.display = 'none';
                if(div1toMany_monthly) div1toMany_monthly.style.display = 'block';
            } else {
                if(div1toMany_annual) div1toMany_annual.style.display = 'none';
                if(div1toMany_quarterly) div1toMany_quarterly.style.display = 'none';
                if(div1toMany_monthly) div1toMany_monthly.style.display = 'none';

                if(div1to1_annual) div1to1_annual.style.display = 'none';
                if(div1to1_quarterly) div1to1_quarterly.style.display = 'none';
                if(div1to1_monthly) div1to1_monthly.style.display = 'none';
            }
        }

        recurringRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                handleRecurringChange(this.value);
            });
        });

        // Trigger once on load
        const selectedRecurring = document.querySelector('input[name="recurring_type"]:checked');
        if (selectedRecurring) {
            handleRecurringChange(selectedRecurring.value);
        }
    });
</script>

<script>
    // Quarterly package management
    document.getElementById('addQuarterly').addEventListener('click', function () {
        const wrapper = document.getElementById('quarterlyPackageWrapper');
        const newItem = document.createElement('div');
        newItem.className = 'quarterly-package-item row g-3 mt-2';

        newItem.innerHTML = `
            <div class="col-md-3">
            <label for="monthlyName" class="form-label">Name</label>
                <input type="text" class="form-control" name="quarterly_name[]" placeholder="Name" >
            </div>
            <div class="col-md-3">
                <label class="form-label">Start Date</label>
                <input type="date" class="form-control" name="quarterly_start_date[]" >
            </div>
            <div class="col-md-3">
                <label class="form-label">End Date</label>
                <input type="date" class="form-control" name="quarterly_end_date[]" >
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm removeQuarterly">X</button>
            </div>
        `;
        wrapper.appendChild(newItem);
    });

    document.addEventListener('click', function (e) {
        if (e.target && e.target.classList.contains('removeQuarterly')) {
            e.target.closest('.quarterly-package-item').remove();
        }
    });
</script>

<script>
    let levelIndices = {
        annual: {{ isset($packages->annual_details['levels']) ? count($packages->annual_details['levels']) : 1 }},
        quarterly: {{ isset($packages->quarterly_details['levels']) ? count($packages->quarterly_details['levels']) : 1 }},
        monthly: {{ isset($packages->monthly_details['levels']) ? count($packages->monthly_details['levels']) : 1 }}
    };

    function addLevelRow(packageType) {
        const container = document.getElementById(`${packageType}LevelsContainer`);
        const index = levelIndices[packageType];

        const row = document.createElement('div');
        row.className = 'row level-item mb-2';
        row.innerHTML = `
            <div class="col-md-4">
                <input type="text" class="form-control" name="${packageType}_levels[${index}][name]" placeholder="Level Name">
            </div>
            <div class="col-md-4">
                <textarea class="form-control" name="${packageType}_levels[${index}][description]" rows="2" placeholder="Description"></textarea>
            </div>
            <div class="col-md-2">
                <input type="number" class="form-control" name="${packageType}_levels[${index}][price]" placeholder="Price">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeLevelRow(this)">Remove</button>
            </div>
        `;
        container.appendChild(row);
        levelIndices[packageType]++;
    }

    function removeLevelRow(button) {
        button.closest('.level-item').remove();
    }
</script>

<script>
    const sessionIndexes = {
        annual: {{ isset($packages->annual_details['sessions']) ? count($packages->annual_details['sessions']) : 1 }},
        quarterly: {{ isset($packages->quarterly_details['sessions']) ? count($packages->quarterly_details['sessions']) : 1 }},
        monthly: {{ isset($packages->monthly_details['sessions']) ? count($packages->monthly_details['sessions']) : 1 }}
    };

    function addSessionRow(packageType) {
        const container = document.getElementById(`sessionSchedule_1to1_${packageType}`);
        const index = sessionIndexes[packageType]++;
        const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        const row = document.createElement('div');
        row.classList.add('row', 'align-items-center', 'mb-2', 'session-row');

        row.innerHTML = `
            <div class="col-md-3">
                <select class="form-select" name="${packageType}_sessions[${index}][day]">
                    <option value="">Select Day</option>
                    ${weekdays.map(day => `<option value="${day}">${day}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][start_time]" />
            </div>
            <div class="col-md-3">
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][end_time]" />
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeSessionRow(this)">Remove</button>
            </div>
        `;

        container.appendChild(row);
    }

    function addSessionRowM(packageType)
    {
        const container = document.getElementById(`sessionSchedule_1tomany_${packageType}`);
        const index = sessionIndexes[packageType]++;

        const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

        const row = document.createElement('div');
        row.classList.add('row', 'align-items-center', 'mb-2', 'session-row');

        row.innerHTML = `
            <div class="col-md-12 mb-2">
                <strong>${packageType}-Slots ${index + 1}</strong>
            </div>

            <div class="col-md-12 mb-2">
                <label class="form-label">Slot name</label>
                <input type="text" class="form-control" name="${packageType}_sessions[${index}][slotname]" placeholder="Slot name">
            </div>

            <div class="col-md-12 mb-2">
                <label class="form-label">Day</label>
                ${days.map(day => `
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" name="${packageType}_sessions[${index}][day][]" value="${day}"> ${day}
                    </div>
                `).join('')}
            </div>

            <div class="col-md-5 mb-2">
                <label class="form-label">Start Time</label>
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][start_time]" />
            </div>

            <div class="col-md-5 mb-2">
                <label class="form-label">End Time</label>
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][end_time]" />
            </div>

            <div class="col-md-2 mb-2">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
            </div>
        `;

        container.appendChild(row);
    }

    function removeSessionRow(button) {
        button.closest('.session-row').remove();
    }
</script>

<script>
    let milestoneCount = {{ isset($milestones) ? count($milestones) : 0 }};
    const sprintCounters = {};

    function addMilestone() {
        const container = document.getElementById('milestoneContainer');

        // Calculate the next milestone number based on how many are currently in the DOM
        const currentMilestones = container.querySelectorAll('.card.shadow-sm.border');
        const milestoneIndex = currentMilestones.length;
        const milestoneId = `milestone_${Date.now()}`;
        const sprintContainerId = `${milestoneId}_sprints`;

        const milestoneHTML = `
            <div class="card shadow-sm border mb-4" id="${milestoneId}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0 text-primary">Milestone ${milestoneIndex + 1}</h5>
                        <button class="btn btn-sm btn-danger" onclick="removeElement('${milestoneId}')">Remove</button>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-12">
                            <label for="milestoneName_${milestoneId}" class="form-label">Milestone Name</label>
                            <input type="text" class="form-control mb-2" id="milestoneName_${milestoneId}" name="milestoneName[]" placeholder="Milestone Name">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="milestoneDesc_${milestoneId}" class="form-label">Milestone Description</label>
                            <textarea class="form-control mb-2" id="milestoneDesc_${milestoneId}" name="milestoneDesc[]" placeholder="Milestone Description"></textarea>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="learningOutcome_${milestoneId}" class="form-label">Learning Outcome</label>
                            <textarea class="form-control mb-3" id="learningOutcome_${milestoneId}" name="learningOutcome[]" placeholder="Learning Outcome"></textarea>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="form-label">Image</label>
                            <input type="file" class="form-control mb-3" name="milestoneImage[]">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="subjects_${milestoneId}" class="form-label">Subjects</label>
                            <select class="form-control select2 mb-3" id="subjects_${milestoneId}" name="subjects[${milestoneId}][]" multiple>
                                <option value="Communication">Communication</option>
                                <option value="Arts">Arts</option>
                                <option value="Maths">Maths</option>
                                <option value="PD">PD</option>
                                <option value="STEM">STEM</option>
                            </select>
                        </div>

                        <div class="form-group col-md-3">
                            <label>Start Date</label>
                            <input type="date" name="milestone_startDate[]" class="form-control" />
                        </div>
                        <div class="form-group col-md-3">
                            <label>End Date</label>
                            <input type="date" name="milestone_endDate[]" class="form-control" />
                        </div>
                        <div class="form-group col-md-3">
                            <label>Milestone Price</label>
                            <input type="number" name="milestone_price[]" class="form-control" />
                        </div>
                        <div class="form-group col-md-3">
                            <label>Milestone Reduction Price</label>
                            <input type="number" name="milestone_reduction_price[]" class="form-control" />
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>Sprints</h6>
                        <div id="sprintContainer_${milestoneId}">
                        </div>
                        <button type="button" class="btn btn-success btn-sm" onclick="addSprint('${milestoneId}')">+ Add Sprint</button>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', milestoneHTML);
        sprintCounters[milestoneId] = 0;
    }

    function removeElement(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.remove();
        }
    }

    function addSprint(milestoneId) {
        const container = document.getElementById(`sprintContainer_${milestoneId}`);
        if (!sprintCounters[milestoneId]) {
            sprintCounters[milestoneId] = 0;
        }
        const sprintNum = ++sprintCounters[milestoneId];
        const sprintId = `${milestoneId}_sprint_${sprintNum}`;
        const sectionContainerId = `${sprintId}_sections`;

        const sprintHTML = `
            <div class="card border mb-3 mt-3 sprint-card" id="${sprintId}">
                <div class="card-body bg-light">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Sprint ${sprintNum}:</h6>
                        <button class="btn btn-sm btn-danger" onclick="removeElement('${sprintId}')">Delete Sprint</button>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-control" name="sprints[${milestoneId}][${sprintNum}][theme]" placeholder="Theme Name" style="width: 200px;">
                        </div>
                        <div class="col-md-9">
                            <label class="form-label">Image</label>
                            <input type="file" class="form-control mb-3" name="sprintImage_${milestoneId}_${sprintNum}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="sprints[${milestoneId}][${sprintNum}][description]" rows="2" placeholder="Description"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Learning Outcome</label>
                            <textarea class="form-control" name="sprints[${milestoneId}][${sprintNum}][learningOutcome]" rows="2" placeholder="Learning Outcome"></textarea>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">Price</label>
                            <input type="number" name="sprints[${milestoneId}][${sprintNum}][price]" class="form-control" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Reduction Price</label>
                            <input type="number" name="sprints[${milestoneId}][${sprintNum}][reductionPrice]" class="form-control" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Slots</label>
                            <input type="number" name="sprintslots[]" class="form-control" />
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Number of Sections</label>
                        <input type="number" class="form-control w-25" name="sprints[${milestoneId}][${sprintNum}][sectionCount]" placeholder="e.g. 3" oninput="generateSections('${sectionContainerId}', this.value)">
                    </div>

                    <div id="${sectionContainerId}"></div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', sprintHTML);
    }

    function generateSections(containerId, count) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        for (let i = 1; i <= count; i++) {
            const sectionHTML = `
                <div class="row g-3 mb-2">
                    <div class="col-md-6">
                        <label class="form-label">Section ${i} Name</label>
                        <input type="text" class="form-control" id="sectionName_${containerId}_${i}" placeholder="Section Name">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Duration (minutes)</label>
                        <input type="number" class="form-control" id="duration_${containerId}_${i}" placeholder="Duration">
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', sectionHTML);
        }
    }
</script>

<script>
    document.getElementById('courseEditForm').addEventListener('submit', function (e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);

        // Get the selected course type
        const selectedType = document.querySelector('input[name="course_type"]:checked').value.trim();

        let typeMapped;
        if (selectedType === 'onlinerecurring') {
            typeMapped = 6;
        } else if (selectedType === 'onlinemilestone') {
            typeMapped = 7;
        } else {
            typeMapped = null; // Handle invalid cases
        }

        // Append course type, location, and other basic fields
        if (typeMapped !== null) {
            formData.append('course_type', typeMapped);
        }

        // Handle recurring type
        const recurringType = document.querySelector('input[name="recurring_type"]:checked');
        if (recurringType) {
            formData.append('recurring_type', recurringType.value);
        }

        // Handle package data for recurring courses
        if (selectedType === 'onlinerecurring') {
            // Annual package data
            const annualName = document.querySelector('input[name="annual_name"]');
            if (annualName && annualName.value) {
                formData.append('annual_name', annualName.value);
            }

            // Collect annual levels
            const annualLevels = [];
            document.querySelectorAll('#annualLevelsContainer .level-item').forEach((levelRow, index) => {
                const name = levelRow.querySelector(`input[name*="[name]"]`);
                const description = levelRow.querySelector(`textarea[name*="[description]"]`);
                const price = levelRow.querySelector(`input[name*="[price]"]`);

                if (name && name.value) {
                    annualLevels.push({
                        name: name.value,
                        description: description ? description.value : '',
                        price: price ? price.value : 0
                    });
                }
            });

            if (annualLevels.length > 0) {
                formData.append('annual_levels', JSON.stringify(annualLevels));
            }

            // Collect annual sessions
            const annualSessions = [];
            document.querySelectorAll('#sessionSchedule_1to1_annual .session-row, #sessionSchedule_1tomany_annual .session-row').forEach((sessionRow, index) => {
                const day = sessionRow.querySelector('select[name*="[day]"]');
                const startTime = sessionRow.querySelector('input[name*="[start_time]"]');
                const endTime = sessionRow.querySelector('input[name*="[end_time]"]');
                const slotName = sessionRow.querySelector('input[name*="[slotname]"]');

                const sessionData = {};
                if (day && day.value) sessionData.day = day.value;
                if (startTime && startTime.value) sessionData.start_time = startTime.value;
                if (endTime && endTime.value) sessionData.end_time = endTime.value;
                if (slotName && slotName.value) sessionData.slotname = slotName.value;

                // Handle checkbox days for 1tomany
                const dayCheckboxes = sessionRow.querySelectorAll('input[name*="[day][]"]:checked');
                if (dayCheckboxes.length > 0) {
                    sessionData.day = Array.from(dayCheckboxes).map(cb => cb.value);
                }

                if (Object.keys(sessionData).length > 0) {
                    annualSessions.push(sessionData);
                }
            });

            if (annualSessions.length > 0) {
                formData.append('annual_sessions', JSON.stringify(annualSessions));
            }
        }

        // Handle milestone data for milestone courses
        if (selectedType === 'onlinemilestone') {
            const milestones = [];

            document.querySelectorAll('#milestoneContainer .card').forEach((milestoneCard, milestoneIndex) => {
                const milestoneData = {
                    name: milestoneCard.querySelector('input[name="milestoneName[]"]')?.value || '',
                    description: milestoneCard.querySelector('textarea[name="milestoneDesc[]"]')?.value || '',
                    learningOutcome: milestoneCard.querySelector('textarea[name="learningOutcome[]"]')?.value || '',
                    startDate: milestoneCard.querySelector('input[name="milestone_startDate[]"]')?.value || '',
                    endDate: milestoneCard.querySelector('input[name="milestone_endDate[]"]')?.value || '',
                    price: milestoneCard.querySelector('input[name="milestone_price[]"]')?.value || 0,
                    reductionPrice: milestoneCard.querySelector('input[name="milestone_reduction_price[]"]')?.value || 0,
                    subjects: [],
                    sprints: []
                };

                // Get subjects
                const subjectSelect = milestoneCard.querySelector('select[name*="subjects"]');
                if (subjectSelect) {
                    milestoneData.subjects = Array.from(subjectSelect.selectedOptions).map(option => option.value);
                }

                // Get sprints
                milestoneCard.querySelectorAll('.sprint-card').forEach((sprintCard, sprintIndex) => {
                    const sprint = {
                        theme: sprintCard.querySelector('input[name*="[theme]"]')?.value || '',
                        description: sprintCard.querySelector('textarea[name*="[description]"]')?.value || '',
                        learningOutcome: sprintCard.querySelector('textarea[name*="[learningOutcome]"]')?.value || '',
                        price: sprintCard.querySelector('input[name*="[price]"]')?.value || 0,
                        reductionPrice: sprintCard.querySelector('input[name*="[reductionPrice]"]')?.value || 0,
                        sequence: sprintIndex + 1,
                        sections: []
                    };

                    // Get sections
                    const sectionRows = sprintCard.querySelectorAll('.row.g-3');
                    sectionRows.forEach((sectionRow, idx) => {
                        const nameInput = sectionRow.querySelector(`input[id^="sectionName"]`);
                        const durationInput = sectionRow.querySelector(`input[id^="duration"]`);

                        if (nameInput && nameInput.value) {
                            sprint.sections.push({
                                name: nameInput.value.trim(),
                                duration: parseInt(durationInput?.value || 0),
                                sequence: idx + 1
                            });
                        }
                    });

                    milestoneData.sprints.push(sprint);
                });

                milestones.push(milestoneData);
            });

            formData.append('milestones', JSON.stringify(milestones));
        }

        // Submit the form
        axios.post("{{ route('organizer.event.updatenewcourseso') }}", formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(res => {
            iziToast.success({ title: 'Success', message: res.data.message });
            // Optional: redirect or reload
            setTimeout(() => {
                window.location.href = "{{ route('organizer.event.all') }}";
            }, 1500);
        })
        .catch(err => {
            const message = err.response?.data?.message || 'Something went wrong';
            iziToast.error({ title: 'Error', message });
        });
    });
</script>
@endpush
