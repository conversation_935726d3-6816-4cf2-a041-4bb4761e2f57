@extends($activeTemplate . 'organizer.layouts.master')
@section('content')

<div class="row">
    <div class="col-lg-12">
        <form id="editCourseoForm" enctype="multipart/form-data">
            @csrf
            <input type="hidden" name="course_id" value="{{ $course->id }}">

            <!-- Course Info -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header">
                    <h5 class="">Edit Course</h5>
                </div>
                <div class="card-body">

                    <!-- Course Type Radio Buttons -->
                    <div class="mb-3">
                        <label class="form-label">Course Type</label>
                        <div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="course_type" id="course_type_onlinerecurring" value="onlinerecurring" {{ $course->type == 6 ? 'checked' : '' }}>
                                <label class="form-check-label" for="course_type_onlinerecurring">Online Recurring</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="course_type" id="course_type_onlinemilestone" value="onlinemilestone" {{ $course->type == 7 ? 'checked' : '' }}>
                                <label class="form-check-label" for="course_type_onlinemilestone">Online Milestone</label>
                            </div>
                        </div>
                        
                        <!-- 1 to 1 / 1 to many toggle section -->
                        <div id="recurringOptions" style="{{ $course->type == 6 ? 'display:block;' : 'display:none;' }}" class="mb-3">
                            <label class="form-label">Recurring Type</label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="recurring_type" id="one_to_one" value="1to1" {{ $course->recurring_type == '1to1' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="one_to_one">1 to 1</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="recurring_type" id="one_to_many" value="1tomany" {{ $course->recurring_type == '1tomany' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="one_to_many">1 to Many</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category & Location -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">Category</label>
                            <select name="category_id" id="category_id" class="form-select" required>
                                <option value="">Select Category</option>
                                @foreach($categories as $cat)
                                <option value="{{ $cat->id }}" {{ $cat->id == $course->category_id ? 'selected' : '' }}>{{ $cat->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="location_id" class="form-label">Location</label>
                            <select name="location_id" id="location_id" class="form-select" required>
                                <option value="">Select Location</option>
                                @foreach($locations as $location)
                                <option value="{{ $location->id }}" {{ $location->id == $course->location_id ? 'selected' : '' }}>{{ $location->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Basic Fields -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="courseName" class="form-label">Course Name</label>
                            <input type="text" class="form-control" id="courseName" name="courseName" value="{{ $course->title }}" placeholder="Enter course name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="shortDescription" class="form-label">Short Description</label>
                            <textarea class="form-control" id="shortDescription" name="shortDescription" placeholder="Enter short description">{{ $course->short_description }}</textarea>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" placeholder="Enter description">{{ $course->description }}</textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="learningoutcome" class="form-label">Learning Outcome</label>
                            <textarea class="form-control" id="learningoutcome" name="learningoutcome" placeholder="Enter learning outcome">{{ $course->learningoutcome }}</textarea>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="age_min" class="form-label">Min Age</label>
                            <input type="number" class="form-control" id="age_min" name="age_min" value="{{ $course->age_min }}" placeholder="Minimum age">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="age_max" class="form-label">Max Age</label>
                            <input type="number" class="form-control" id="age_max" name="age_max" value="{{ $course->age_max }}" placeholder="Maximum age">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">Price</label>
                            <input type="number" class="form-control" id="price" name="price" value="{{ $course->price }}" placeholder="Course price">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="seats" class="form-label">Total Seats</label>
                            <input type="number" name="seats" id="seats" class="form-control" placeholder="Enter seats" value="{{ $course->seats }}">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="courseImage" class="form-label">Course Image</label>
                        <input type="file" class="form-control" id="courseImage" name="courseImage">
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="{{ $course->start_date }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="{{ $course->end_date }}" required>
                        </div>
                    </div>

                    <!-- Package Fields (same structure as create page) -->
                    <div id="packageFields" style="{{ $course->type == 6 ? 'display:block;' : 'display:none;' }}">
                        <!-- Annual package -->
                        <div class="AnnualpackageList">
                            <h6>Annual package</h6>
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="annualName" class="form-label">Name</label>
                                        <input type="text" class="form-control" id="annualName" name="annual_name" placeholder="Name" value="{{ $packages->annual_details['name'] ?? '' }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Start Date</label>
                                    <input type="date" class="form-control" name="annual_startDate[]" value="{{ $packages->annual_details['start_date'] ?? '' }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">End Date</label>
                                    <input type="date" class="form-control" name="annual_endDate[]" value="{{ $packages->annual_details['end_date'] ?? '' }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Reduction Price (₹)</label>
                                    <input type="number" class="form-control" name="annual_reduction[]" value="{{ $packages->annual_details['reduction_price'] ?? '' }}">
                                </div>
                            </div>

                            <div class="row">
                                <h6>Level</h6>
                                <div id="annualLevelsContainer">
                                    @if(!empty($packages->annual_details['levels']))
                                        @foreach($packages->annual_details['levels'] as $index => $level)
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="annual_levels[{{ $index }}][name]" placeholder="Level name" value="{{ $level['name'] ?? '' }}">
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="annual_levels[{{ $index }}][description]" rows="2" placeholder="Description">{{ $level['description'] ?? '' }}</textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="annual_levels[{{ $index }}][price]" placeholder="Price" value="{{ $level['price'] ?? '' }}">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                @if($index > 0)
                                                <button type="button" class="btn btn-danger btn-sm" onclick="removeLevelRow(this)">Remove</button>
                                                @endif
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="annual_levels[0][name]" placeholder="Level name">
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="annual_levels[0][description]" rows="2" placeholder="Description"></textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="annual_levels[0][price]" placeholder="Price">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                <!-- Remove button for dynamically added rows -->
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <div class="mb-3">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addLevelRow('annual')">+ Add More</button>
                                </div>

                                <!-- Common fields for all levels -->
                                <div class="row">
                                    
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" class="form-control" name="annual_image">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Max. Slots/Week</label>
                                        <input type="number" class="form-control" name="annual_slots" placeholder="" value="{{ $packages->annual_details['max_slots'] ?? '' }}">
                                    </div>
                                </div>
                            </div>

                            <!-- Session Schedule for Annual -->
                            <div class="form-group mt-4" id="sessionSchedule_1to1_annual">
                                <label>Session Schedule</label>
                                <div>
                                    @if(!empty($packages->annual_details['sessions']))
                                        @foreach($packages->annual_details['sessions'] as $i => $session)
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-3">
                                                <label>Day</label>
                                                <select class="form-select" name="annual_sessions[{{ $i }}][day]">
                                                    <option value="">Select Day</option>
                                                    @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                        <option value="{{ $day }}" {{ ($session['day'] ?? '') == $day ? 'selected' : '' }}>{{ $day }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label>Start Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[{{ $i }}][start_time]" value="{{ $session['start_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-3">
                                                <label>End Time</label>
                                                <input type="time" class="form-control" name="annual_sessions[{{ $i }}][end_time]" value="{{ $session['end_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                            </div>
                                        </div>
                                        @endforeach
                                    @endif
                                    <button type="button" class="btn btn-primary btn-sm mb-2" onclick="addSessionRow('annual')">+ Add Session</button>
                                </div>
                            </div>

                            <div class="form-group mt-4" id="sessionSchedule_1tomany_annual" style="display:none;">
                                <label>Session Schedule (1 to Many)</label>
                                
                                <div id="annualSessionsWrapper">
                                    @if(!empty($packages->annual_details['sessions']))
                                        @foreach($packages->annual_details['sessions'] as $i => $slot)
                                            @php
                                                $sessionList = !empty($slot['sessions']) ? $slot['sessions'] : [ [] ];
                                            @endphp
                                
                                            @foreach($sessionList as $j => $session)
                                            <div class="row align-items-center mb-2 session-row">
                                                <div class="col-md-12 mb-2">
                                                    <label class="form-label">Slot name</label>
                                                    <input type="text" class="form-control"
                                                           name="1tomany_annual_sessions[{{ $i }}][slotname]"
                                                           placeholder="Slot name"
                                                           value="{{ $slot['slotname'] ?? '' }}">
                                                </div>
                                
                                                <div class="col-md-12">
                                                    <label class="form-label">Day</label><br>
                                                    @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="checkbox"
                                                                   name="1tomany_annual_sessions[{{ $i }}][sessions][{{ $j }}][day][]"
                                                                   value="{{ $day }}"
                                                                   {{ (!empty($session['day']) && (is_array($session['day']) ? in_array($day, $session['day']) : $session['day'] === $day)) ? 'checked' : '' }}>
                                                            {{ $day }}
                                                        </div>
                                                    @endforeach
                                                </div>
                                
                                                <div class="col-md-5">
                                                    <label class="form-label">Start Time</label>
                                                    <input type="time" class="form-control"
                                                           name="1tomany_annual_sessions[{{ $i }}][sessions][{{ $j }}][start_time]"
                                                           value="{{ $session['start_time'] ?? '' }}">
                                                </div>
                                
                                                <div class="col-md-5">
                                                    <label class="form-label">End Time</label>
                                                    <input type="time" class="form-control"
                                                           name="1tomany_annual_sessions[{{ $i }}][sessions][{{ $j }}][end_time]"
                                                           value="{{ $session['end_time'] ?? '' }}">
                                                </div>
                                
                                                <div class="col-md-2 d-flex align-items-end">
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                                </div>
                                            </div>
                                            @endforeach
                                        @endforeach
                                    @else
                                        {{-- No data at all: render default slot/session row --}}
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-12 mb-2">
                                                <label class="form-label">Slot name</label>
                                                <input type="text" class="form-control"
                                                       name="1tomany_annual_sessions[0][slotname]"
                                                       placeholder="Slot name">
                                            </div>
                                
                                            <div class="col-md-12">
                                                <label class="form-label">Day</label><br>
                                                @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox"
                                                               name="1tomany_annual_sessions[0][sessions][0][day][]"
                                                               value="{{ $day }}">
                                                        {{ $day }}
                                                    </div>
                                                @endforeach
                                            </div>
                                
                                            <div class="col-md-5">
                                                <label class="form-label">Start Time</label>
                                                <input type="time" class="form-control"
                                                       name="1tomany_annual_sessions[0][sessions][0][start_time]">
                                            </div>
                                
                                            <div class="col-md-5">
                                                <label class="form-label">End Time</label>
                                                <input type="time" class="form-control"
                                                       name="1tomany_annual_sessions[0][sessions][0][end_time]">
                                            </div>
                                
                                            <div class="col-md-2 d-flex align-items-end">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                            </div>
                                        </div>
                                    @endif
                                    <button type="button" class="btn btn-primary btn-sm mb-2" onclick="addSessionRowM('annual')">+ Add Session</button>
                                </div>
                                
                            </div>
                        </div>

                        <!-- Quarterly package -->
                        <div class="QuarterlypackageList">
                            <h6>Quarterly package</h6>
                            
                            <div id="quarterlyPackageWrapper">
                                @if(!empty($packages->quarterly_details['packages']) && count($packages->quarterly_details['packages']) > 0)
                                    @foreach($packages->quarterly_details['packages'] as $index => $quarterly)
                                    <div class="quarterly-package-item row g-3 {{ $index > 0 ? 'mt-2' : '' }}">
                                        <div class="col-md-3">
                                            <label>Name</label>
                                            <input type="text" class="form-control" name="quarterly_name[]" placeholder="Name" value="{{ $quarterly['name'] ?? '' }}">
                                        </div>
                                        <div class="col-md-3">
                                            <label>Start Date</label>
                                            <input type="date" class="form-control" name="quarterly_start_date[]" value="{{ $quarterly['start_date'] ?? '' }}">
                                        </div>
                                        <div class="col-md-3">
                                            <label>End Date</label>
                                            <input type="date" class="form-control" name="quarterly_end_date[]" value="{{ $quarterly['end_date'] ?? '' }}">
                                        </div>
                                        <div class="col-md-3 d-flex align-items-end">
                                            @if($index > 0)
                                            <button type="button" class="btn btn-danger btn-sm removeQuarterly">X</button>
                                            @endif
                                        </div>
                                    </div>
                                    @endforeach
                                @else
                                    <div class="quarterly-package-item row g-3">
                                        <div class="col-md-3">
                                            <label>Name</label>
                                            <input type="text" class="form-control" name="quarterly_name[]" placeholder="Name">
                                        </div>
                                        <div class="col-md-3">
                                            <label>Start Date</label>
                                            <input type="date" class="form-control" name="quarterly_start_date[]">
                                        </div>
                                        <div class="col-md-3">
                                            <label>End Date</label>
                                            <input type="date" class="form-control" name="quarterly_end_date[]">
                                        </div>
                                        <div class="col-md-3 d-flex align-items-end">
                                            <!-- No remove button for first default row -->
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-primary" id="addQuarterly">+ Add More</button>
                            </div>

                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">Reduction Price (₹)</label>
                                    <input type="number" class="form-control" name="quarterly_reduction" value="{{ $packages->quarterly_details['reduction_price'] ?? '' }}">
                                </div>
                            </div>
                            
                            <div class="row">
                                <h6>Level</h6>
                                <div id="quarterlyLevelsContainer">
                                    @if(!empty($packages->quarterly_details['levels']))
                                        @foreach($packages->quarterly_details['levels'] as $index => $level)
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="quarterly_levels[{{ $index }}][name]" placeholder="Level name" value="{{ $level['name'] ?? '' }}">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="quarterly_levels[{{ $index }}][description]" rows="2" placeholder="Description">{{ $level['description'] ?? '' }}</textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="quarterly_levels[{{ $index }}][price]" placeholder="Price" value="{{ $level['price'] ?? '' }}">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                @if($index > 0)
                                                <button type="button" class="btn btn-danger btn-sm" onclick="removeLevelRow(this)">Remove</button>
                                                @endif
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="quarterly_levels[0][name]" placeholder="Level name">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="quarterly_levels[0][description]" rows="2" placeholder="Description"></textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="quarterly_levels[0][price]" placeholder="Price">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                <!-- No remove button for first level -->
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                
                                <div class="mb-3">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addLevelRow('quarterly')">+ Add More</button>
                                </div>

                                <!-- Common fields for all levels -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" class="form-control" name="quarterly_image">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Max. Slots/Week</label>
                                        <input type="number" class="form-control" name="quarterly_slots" value="{{ $packages->quarterly_details['max_slots'] ?? '' }}">
                                    </div>
                                </div>
                            </div>

                            <!-- Session Schedule for Quarterly -->
                            <div class="form-group mt-4" id="sessionSchedule_1to1_quarterly">
                                <label>Session Schedule</label>
                                <div>
                                    @if(!empty($packages->quarterly_details['sessions']))
                                        @foreach($packages->quarterly_details['sessions'] as $i => $session)
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-3">
                                                <label>Day</label>
                                                <select class="form-select" name="quarterly_sessions[{{ $i }}][day]">
                                                    <option value="">Select Day</option>
                                                    @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                        <option value="{{ $day }}" {{ ($session['day'] ?? '') == $day ? 'selected' : '' }}>{{ $day }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label>Start Time</label>
                                                <input type="time" class="form-control" name="quarterly_sessions[{{ $i }}][start_time]" value="{{ $session['start_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-3">
                                                <label>End Time</label>
                                                <input type="time" class="form-control" name="quarterly_sessions[{{ $i }}][end_time]" value="{{ $session['end_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <!-- Default first session row when no existing sessions -->
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-3">
                                                <label>Day</label>
                                                <select class="form-select" name="quarterly_sessions[0][day]">
                                                    <option value="">Select Day</option>
                                                    @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                        <option value="{{ $day }}">{{ $day }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label>Start Time</label>
                                                <input type="time" class="form-control" name="quarterly_sessions[0][start_time]" />
                                            </div>
                                            <div class="col-md-3">
                                                <label>End Time</label>
                                                <input type="time" class="form-control" name="quarterly_sessions[0][end_time]" />
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                            </div>
                                        </div>
                                    @endif
                                    <button type="button" class="btn btn-primary btn-sm mb-2" onclick="addSessionRow('quarterly')">+ Add Session</button>
                                </div>
                            </div>

                            <div class="form-group mt-4" id="sessionSchedule_1tomany_quarterly" style="display:none;">
                                <label>Session Schedule (1 to Many)</label>
                                <div>
                                    @php
                                        $sessions = $packages->quarterly_details['sessions'] ?? [];
                                        @endphp
                                        
                                        @if(!empty($sessions))
                                            @foreach($sessions as $i => $slot)
                                                @php
                                                    $groupedSessions = [];
                                        
                                                    if (!empty($slot['sessions'])) {
                                                        foreach ($slot['sessions'] as $sess) {
                                                            // Normalize times and group by start-end time
                                                            $start = $sess['start_time'] ?? '';
                                                            $end = $sess['end_time'] ?? '';
                                                            $key = "$start-$end";
                                        
                                                            $groupedSessions[$key]['start_time'] = $start;
                                                            $groupedSessions[$key]['end_time'] = $end;
                                                            $groupedSessions[$key]['days'][] = $sess['day'];
                                                        }
                                                    } else {
                                                        // At least one empty slot for the UI
                                                        $groupedSessions['empty'] = [
                                                            'start_time' => '',
                                                            'end_time' => '',
                                                            'days' => []
                                                        ];
                                                    }
                                                @endphp
                                        
                                                @foreach($groupedSessions as $j => $group)
                                                    <div class="row align-items-center mb-2 session-row">
                                                        <div class="col-md-12 mb-2">
                                                            <label class="form-label">Slot name</label>
                                                            <input type="text" class="form-control"
                                                                   name="1tomany_quarterly_sessions[{{ $i }}][slotname]"
                                                                   placeholder="Slot name"
                                                                   value="{{ $slot['slotname'] ?? '' }}">
                                                        </div>
                                        
                                                        <div class="col-md-12">
                                                            <label class="form-label">Day</label><br>
                                                            @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                                <div class="form-check form-check-inline">
                                                                    <input class="form-check-input" type="checkbox"
                                                                           name="1tomany_quarterly_sessions[{{ $i }}][sessions][{{ $j }}][day][]"
                                                                           value="{{ $day }}"
                                                                           {{ in_array($day, $group['days']) ? 'checked' : '' }}>
                                                                    {{ $day }}
                                                                </div>
                                                            @endforeach
                                                        </div>
                                        
                                                        <div class="col-md-5">
                                                            <label class="form-label">Start Time</label>
                                                            <input type="time" class="form-control"
                                                                   name="1tomany_quarterly_sessions[{{ $i }}][sessions][{{ $j }}][start_time]"
                                                                   value="{{ $group['start_time'] }}">
                                                        </div>
                                        
                                                        <div class="col-md-5">
                                                            <label class="form-label">End Time</label>
                                                            <input type="time" class="form-control"
                                                                   name="1tomany_quarterly_sessions[{{ $i }}][sessions][{{ $j }}][end_time]"
                                                                   value="{{ $group['end_time'] }}">
                                                        </div>
                                        
                                                        <div class="col-md-2 d-flex align-items-end">
                                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endforeach
                                        @else
                                            {{-- No session data, show 1 default row --}}
                                            <div class="row align-items-center mb-2 session-row">
                                                <div class="col-md-12 mb-2">
                                                    <label class="form-label">Slot name</label>
                                                    <input type="text" class="form-control"
                                                           name="1tomany_quarterly_sessions[0][slotname]"
                                                           placeholder="Slot name">
                                                </div>
                                        
                                                <div class="col-md-12">
                                                    <label class="form-label">Day</label><br>
                                                    @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="checkbox"
                                                                   name="1tomany_quarterly_sessions[0][sessions][0][day][]"
                                                                   value="{{ $day }}">
                                                            {{ $day }}
                                                        </div>
                                                    @endforeach
                                                </div>
                                        
                                                <div class="col-md-5">
                                                    <label class="form-label">Start Time</label>
                                                    <input type="time" class="form-control"
                                                           name="1tomany_quarterly_sessions[0][sessions][0][start_time]">
                                                </div>
                                        
                                                <div class="col-md-5">
                                                    <label class="form-label">End Time</label>
                                                    <input type="time" class="form-control"
                                                           name="1tomany_quarterly_sessions[0][sessions][0][end_time]">
                                                </div>
                                        
                                                <div class="col-md-2 d-flex align-items-end">
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                                </div>
                                            </div>
                                        @endif

                                    <button type="button" class="btn btn-primary btn-sm mb-2" onclick="addSessionRowM('quarterly')">+ Add Session</button>
                                </div>
                            </div>
                        </div>

                        <!-- Monthly package -->
                        <div class="MonthlypackageList">
                            <h6>Monthly package</h6>
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="monthlyName" class="form-label">Name</label>
                                        <input type="text" class="form-control" id="monthlyName" name="monthly_name" placeholder="Name" value="{{ $packages->monthly_details['name'] ?? '' }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Start Date</label>
                                    <input type="date" class="form-control" name="monthly_startDate[]" value="{{ $packages->monthly_details['start_date'] ?? '' }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">End Date</label>
                                    <input type="date" class="form-control" name="monthly_endDate[]" value="{{ $packages->monthly_details['end_date'] ?? '' }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Reduction Price (₹)</label>
                                    <input type="number" class="form-control" name="monthly_reduction[]" value="{{ $packages->monthly_details['reduction_price'] ?? '' }}">
                                </div>
                            </div>
                            
                            <div class="row">
                                <h6>Level</h6>
                                
                                <div id="monthlyLevelsContainer">
                                    @if(!empty($packages->monthly_details['levels']))
                                        @foreach($packages->monthly_details['levels'] as $index => $level)
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="monthly_levels[{{ $index }}][name]" placeholder="Level name" value="{{ $level['name'] ?? '' }}">
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="monthly_levels[{{ $index }}][description]" rows="2" placeholder="Description">{{ $level['description'] ?? '' }}</textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="monthly_levels[{{ $index }}][price]" placeholder="Price" value="{{ $level['price'] ?? '' }}">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                @if($index > 0)
                                                <button type="button" class="btn btn-danger btn-sm" onclick="removeLevelRow(this)">Remove</button>
                                                @endif
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <div class="row level-item mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Name</label>
                                                <input type="text" class="form-control" name="monthly_levels[0][name]" placeholder="Level name">
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" name="monthly_levels[0][description]" rows="2" placeholder="Description"></textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" class="form-control" name="monthly_levels[0][price]" placeholder="Price">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                <!-- Remove button shown only on dynamically added rows -->
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <div class="mb-3">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addLevelRow('monthly')">+ Add More</button>
                                </div>
                                    
                                <div class="row">
                                    
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" class="form-control" name="monthly_image">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Max. Slots/Week</label>
                                        <input type="number" class="form-control" name="monthly_slots" value="{{ $packages->monthly_details['max_slots'] ?? '' }}">
                                    </div>
                                </div>
                            </div>

                            <!-- Session Schedule for Monthly -->
                            <div class="form-group mt-4" id="sessionSchedule_1to1_monthly" style="display: {{ $course->online_type == '1to1' ? 'block' : 'none' }};">
                                <label>Session Schedule</label>
                                <div>
                                    @if(!empty($packages->monthly_details['sessions']))
                                        @foreach($packages->monthly_details['sessions'] as $i => $session)
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-3">
                                                <label>Day</label>
                                                <select class="form-select" name="monthly_sessions[{{ $i }}][day]">
                                                    <option value="">Select Day</option>
                                                    @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                        <option value="{{ $day }}" {{ ($session['day'] ?? '') == $day ? 'selected' : '' }}>{{ $day }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label>Start Time</label>
                                                <input type="time" class="form-control" name="monthly_sessions[{{ $i }}][start_time]" value="{{ $session['start_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-3">
                                                <label>End Time</label>
                                                <input type="time" class="form-control" name="monthly_sessions[{{ $i }}][end_time]" value="{{ $session['end_time'] ?? '' }}" />
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <!-- Default first session row when no existing sessions -->
                                        <div class="row align-items-center mb-2 session-row">
                                            <div class="col-md-3">
                                                <label>Day</label>
                                                <select class="form-select" name="monthly_sessions[0][day]">
                                                    <option value="">Select Day</option>
                                                    @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                        <option value="{{ $day }}">{{ $day }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label>Start Time</label>
                                                <input type="time" class="form-control" name="monthly_sessions[0][start_time]" />
                                            </div>
                                            <div class="col-md-3">
                                                <label>End Time</label>
                                                <input type="time" class="form-control" name="monthly_sessions[0][end_time]" />
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">Remove</button>
                                            </div>
                                        </div>
                                    @endif
                                    <button type="button" class="btn btn-primary btn-sm mb-2" onclick="addSessionRow('monthly')">+ Add Session</button>
                                </div>
                            </div>

                            <div class="form-group mt-4" id="sessionSchedule_1tomany_monthly" style="display: {{ $course->online_type == '1tomany' ? 'block' : 'none' }};">
                                <label>Session Schedule (1 to Many)</label>
                                <div>
                                        @if(!empty($packages->monthly_details['sessions']))
                                            @foreach($packages->monthly_details['sessions'] as $i => $slot)
                                                
                                                <div class="row align-items-center mb-2 session-row">
                                                    <div class="col-md-12 mb-2">
                                                        <label class="form-label">Slot name</label>
                                                        <input type="text" class="form-control" name="1tomany_monthly_sessions[{{ $i }}][slotname]" placeholder="Slot name" value="{{ $slot['slotname'] ?? '' }}">
                                                    </div>
                                                    @foreach($slot['sessions'] as $j => $session)
                                                    <div class="col-md-12">
                                                        <label class="form-label">Day</label><br>
                                                        @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                            <div class="form-check form-check-inline">
                                                                <input class="form-check-input" type="checkbox"
                                                                    name="1tomany_monthly_sessions[{{ $i }}][sessions][{{ $j }}][day][]"
                                                                    value="{{ $day }}"
                                                                    {{ (isset($session['day']) && $session['day'] == $day) || (is_array($session['day'] ?? null) && in_array($day, $session['day'])) ? 'checked' : '' }}>
                                                                {{ $day }}
                                                            </div>
                                                        @endforeach
                                                    </div>
                                    
                                                    <div class="col-md-5">
                                                        <label class="form-label">Start Time</label>
                                                        <input type="time" class="form-control" name="1tomany_monthly_sessions[{{ $i }}][sessions][{{ $i }}][start_time]" value="{{ $session['start_time'] ?? '' }}">
                                                    </div>
                                    
                                                    <div class="col-md-5">
                                                        <label class="form-label">End Time</label>
                                                        <input type="time" class="form-control" name="1tomany_monthly_sessions[{{ $i }}][sessions][{{ $i }}][end_time]"  value="{{ $session['end_time'] ?? '' }}">
                                                    </div>
                                    
                                                    <div class="col-md-2 d-flex align-items-end">
                                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                                    </div>
                                                    @endforeach
                                                </div>
                                                
                                            @endforeach
                                        @else
                                            {{-- Default empty row --}}
                                            <div class="row align-items-center mb-2 session-row">
                                                <div class="col-md-12 mb-2">
                                                    <label class="form-label">Slot name</label>
                                                    <input type="text" class="form-control" name="1tomany_monthly_sessions[0][slotname]" placeholder="Slot name">
                                                </div>
                                    
                                                <div class="col-md-12">
                                                    <label class="form-label">Day</label><br>
                                                    @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $day)
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="checkbox" name="1tomany_monthly_sessions[0][sessions][0][day][]" value="{{ $day }}"> {{ $day }}
                                                        </div>
                                                    @endforeach
                                                </div>
                                    
                                                <div class="col-md-5">
                                                    <label class="form-label">Start Time</label>
                                                    <input type="time" class="form-control" name="1tomany_monthly_sessions[0][sessions][0][start_time]">
                                                </div>
                                    
                                                <div class="col-md-5">
                                                    <label class="form-label">End Time</label>
                                                    <input type="time" class="form-control" name="1tomany_monthly_sessions[0][sessions][0][end_time]">
                                                </div>
                                    
                                                <div class="col-md-2 d-flex align-items-end">
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
                                                </div>
                                            </div>
                                        @endif
                                    <button type="button" class="btn btn-primary btn-sm mb-2" onclick="addSessionRowM('monthly')">+ Add Session</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Milestone Section -->
                    <div id="milestoneSection" style="{{ $course->type == 7 ? 'display:block;' : 'display:none;' }}">
                        <div id="milestoneContainer"></div>
                        <button type="button" class="btn btn-success mb-3" onclick="addMilestone()">+ Add Milestone</button>
                    </div>

                    <!-- Submit -->
                    <div class="mt-4" style="float:right">
                        <button type="submit" class="btn btn-primary">Update Course</button>
                    </div>

                </div>
            </div>
        </form>
    </div>
</div>

<x-confirmation-modal />
@endsection

@push('breadcrumb-plugins')
    <x-search-form placeholder="Name | Organizer" />
@endpush

@push('script')
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

<script>
    let levelIndices = {
        annual: {{ !empty($packages->annual_details['levels']) ? count($packages->annual_details['levels']) : 1 }},
        quarterly: 1,
        monthly: 1
    };

    // Course type change handler
    document.addEventListener('DOMContentLoaded', function () {
        const typeRadios = document.querySelectorAll('input[name="course_type"]');
        const milestoneSection = document.getElementById('milestoneSection');
        const packageFields = document.getElementById('packageFields');
        const recurringOptions = document.getElementById('recurringOptions');

        function handleCourseTypeChange(value) {
            if (value === 'onlinemilestone') {
                milestoneSection.style.display = 'block';
                packageFields.style.display = 'none';
                recurringOptions.style.display = 'none';
            } else if (value === 'onlinerecurring') {
                milestoneSection.style.display = 'none';
                packageFields.style.display = 'block';
                recurringOptions.style.display = 'block';
            }
        }

        typeRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                handleCourseTypeChange(this.value);
            });
        });

        // Handle recurring type changes
        const recurringRadios = document.querySelectorAll('input[name="recurring_type"]');
        
        function handleRecurringChange(value) {
            ['annual', 'quarterly', 'monthly'].forEach(packageType => {
                const div1to1 = document.getElementById(`sessionSchedule_1to1_${packageType}`);
                const div1toMany = document.getElementById(`sessionSchedule_1tomany_${packageType}`);
                
                if (div1to1 && div1toMany) {
                    if (value === '1to1') {
                        div1to1.style.display = 'block';
                        div1toMany.style.display = 'none';
                    } else if (value === '1tomany') {
                        div1to1.style.display = 'none';
                        div1toMany.style.display = 'block';
                    }
                }
            });
        }

        recurringRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                handleRecurringChange(this.value);
            });
        });

        // Initial trigger
        const selectedRecurring = document.querySelector('input[name="recurring_type"]:checked');
        if (selectedRecurring) {
            handleRecurringChange(selectedRecurring.value);
        }

        // Pre-fill milestones if editing a milestone-based course
        @if($course->type == 7 && isset($milestones) && $milestones->count())
            const milestonesData = @json($milestones);
            milestonesData.forEach((ms, idx) => {
                addMilestone(ms, idx);
            });
        @endif
    });

    // Form submission
    document.getElementById('editCourseoForm').addEventListener('submit', function (e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        
        // Get the selected course type
        const selectedType = document.querySelector('input[name="course_type"]:checked').value.trim();
        let typeMapped = selectedType === 'onlinerecurring' ? 6 : 7;
        
        formData.append('course_type', typeMapped);

        // Handle milestones for milestone-based courses
        if (typeMapped === 7) {
            const milestones = [];
            const milestoneCards = document.querySelectorAll('#milestoneContainer > .milestone-card');
            
            milestoneCards.forEach((card, mi) => {
                const milestoneData = {
                    name: card.querySelector('input[name*="[name]"]').value.trim(),
                    description: card.querySelector('textarea[name*="[description]"]').value.trim(),
                    learningOutcome: card.querySelector('textarea[name*="[learningOutcome]"]').value.trim(),
                    price: card.querySelector('input[name*="[price]"]')?.value || null,
                    sequence: mi + 1,
                    sprints: []
                };

                // Handle sprints
                const sprintCards = card.querySelectorAll('.sprint-card');
                sprintCards.forEach((sprintCard, si) => {
                    const sprint = {
                        theme: sprintCard.querySelector('input[placeholder="Theme Name"]').value.trim(),
                        description: sprintCard.querySelector('textarea[name*="[description]"]').value.trim(),
                        learningOutcome: sprintCard.querySelector('textarea[name*="[learningOutcome]"]').value.trim(),
                        sequence: si + 1,
                        sections: []
                    };

                    // Handle sections
                    const sectionRows = sprintCard.querySelectorAll('.row.g-3');
                    sectionRows.forEach((sectionRow, idx) => {
                        const nameInput = sectionRow.querySelector('input[id^="sectionName"]');
                        const durationInput = sectionRow.querySelector('input[id^="duration"]');
                        
                        if (nameInput && nameInput.value.trim()) {
                            sprint.sections.push({
                                name: nameInput.value.trim(),
                                duration: parseInt(durationInput?.value || 0),
                                sequence: idx + 1
                            });
                        }
                    });

                    milestoneData.sprints.push(sprint);
                });

                milestones.push(milestoneData);
            });

            formData.append('milestones', JSON.stringify(milestones));
        }

        fetch("{{ route('organizer.event.updatenewcourseso') }}", {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
            },
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                iziToast.success({ title: 'Success', message: result.message });
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                iziToast.error({ title: 'Error', message: result.message });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            iziToast.error({ title: 'Error', message: 'Something went wrong' });
        });
    });

    // Copy all helper functions from create page
    function addLevelRow(packageType) {
        const container = document.getElementById(`${packageType}LevelsContainer`);
        const index = levelIndices[packageType];
        
        const row = document.createElement('div');
        row.className = 'row level-item mb-3';
        row.innerHTML = `
            <div class="col-md-4">
                <label class="form-label">Name</label>
                <input type="text" class="form-control" name="${packageType}_levels[${index}][name]" placeholder="Level name">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">Description</label>
                <textarea class="form-control" name="${packageType}_levels[${index}][description]" rows="2" placeholder="Description"></textarea>
            </div>
            <div class="col-md-2">
                <label class="form-label">Price</label>
                <input type="number" class="form-control" name="${packageType}_levels[${index}][price]" placeholder="Price">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeLevelRow(this)">Remove</button>
            </div>
        `;
        
        container.appendChild(row);
        levelIndices[packageType]++;
    }

    function removeLevelRow(button) {
        button.closest('.level-item').remove();
    }

    function addSessionRow(packageType) {
        const container = document.getElementById(`sessionSchedule_1to1_${packageType}`);
        const sessionRows = container.querySelectorAll('.session-row');
        const index = sessionRows.length;
        const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        const row = document.createElement('div');
        row.classList.add('row', 'align-items-center', 'mb-2', 'session-row');

        row.innerHTML = `
            <div class="col-md-3">
                <label>Day</label>
                <select class="form-select" name="${packageType}_sessions[${index}][day]">
                    <option value="">Select Day</option>
                    ${weekdays.map(day => `<option value="${day}">${day}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <label>Start Time</label>
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][start_time]" />
            </div>
            <div class="col-md-3">
                <label>End Time</label>
                <input type="time" class="form-control" name="${packageType}_sessions[${index}][end_time]" />
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeSessionRow(this)">Remove</button>
            </div>
        `;

        container.appendChild(row);
    }

    function removeSessionRow(button) {
        button.closest('.session-row').remove();
    }

    // Milestone and sprint functions (copy from create page)
    let milestoneCounter = 0;
    let sprintCounters = {};

    function addMilestone(existingData = null, index = null) {
        const milestoneId = index !== null ? index : milestoneCounter++;
        const sprintContainerId = `sprintContainer_${milestoneId}`;
        
        const milestoneHTML = `
            <div class="card border mb-3 milestone-card" id="milestone_${milestoneId}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">Milestone ${milestoneId + 1}</h5>
                        <button class="btn btn-sm btn-danger" onclick="removeElement('milestone_${milestoneId}')">Delete Milestone</button>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-control" name="milestones[${milestoneId}][name]" value="${existingData?.name || ''}" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Price</label>
                            <input type="number" class="form-control" name="milestones[${milestoneId}][price]" value="${existingData?.price || ''}">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="milestones[${milestoneId}][description]" rows="3">${existingData?.description || ''}</textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Learning Outcome</label>
                            <textarea class="form-control" name="milestones[${milestoneId}][learningOutcome]" rows="3">${existingData?.learningOutcome || ''}</textarea>
                        </div>
                    </div>
                    
                    <div id="${sprintContainerId}"></div>
                    <button type="button" class="btn btn-primary btn-sm mt-2" onclick="addSprint('${sprintContainerId}', '${milestoneId}')">+ Add Sprint</button>
                </div>
            </div>
        `;
        
        document.getElementById('milestoneContainer').insertAdjacentHTML('beforeend', milestoneHTML);
        sprintCounters[milestoneId] = 0;
        
        // Add existing sprints if any
        if (existingData?.sprints) {
            existingData.sprints.forEach(sprint => {
                addSprint(sprintContainerId, milestoneId, sprint);
            });
        }
    }

    function addSprint(targetId, milestoneId, existingData = null) {
        if (!sprintCounters[milestoneId]) sprintCounters[milestoneId] = 0;
        const sprintNum = ++sprintCounters[milestoneId];
        const sprintId = `sprint_${milestoneId}_${sprintNum}_${Date.now()}`;
        const sectionContainerId = `${sprintId}_sections`;

        const sprintHTML = `
            <div class="card border mb-3 mt-3 sprint-card" id="${sprintId}">
                <div class="card-body bg-light">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Sprint ${sprintNum}:</h6>
                        <button class="btn btn-sm btn-danger" onclick="removeElement('${sprintId}')">Delete Sprint</button>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-control" name="sprints[${milestoneId}][${sprintNum}][theme]" placeholder="Theme Name" value="${existingData?.theme || ''}" style="width: 200px;">
                        </div>
                        <div class="col-md-9">
                            <label class="form-label">Image</label>
                            <input type="file" class="form-control mb-3" name="sprintImage_${milestoneId}_${sprintNum}">
                        </div>
                    </div> 
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="sprints[${milestoneId}][${sprintNum}][description]" rows="3">${existingData?.description || ''}</textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Learning Outcome</label>
                            <textarea class="form-control" name="sprints[${milestoneId}][${sprintNum}][learningOutcome]" rows="3">${existingData?.learningOutcome || ''}</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Number of Sections</label>
                        <input type="number" class="form-control w-25" name="sprints[${milestoneId}][${sprintNum}][sectionCount]" placeholder="e.g. 3" oninput="generateSections('${sectionContainerId}', this.value)" value="${existingData?.sections?.length || ''}">
                    </div>

                    <div id="${sectionContainerId}"></div>
                </div>
            </div>
        `;
        document.getElementById(targetId).insertAdjacentHTML('beforeend', sprintHTML);
        
        // Generate sections if existing data has sections
        if (existingData?.sections?.length) {
            generateSections(sectionContainerId, existingData.sections.length);
            // Fill section data
            existingData.sections.forEach((section, idx) => {
                const nameInput = document.querySelector(`#sectionName_${sectionContainerId}_${idx}`);
                const durationInput = document.querySelector(`#duration_${sectionContainerId}_${idx}`);
                if (nameInput) nameInput.value = section.name || '';
                if (durationInput) durationInput.value = section.duration || '';
            });
        }
    }

    function generateSections(containerId, count) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        count = parseInt(count);
        if (isNaN(count) || count < 1) return;

        for (let i = 0; i < count; i++) {
            const sectionHTML = `
                <div class="row g-3 align-items-end mb-2 border-bottom pb-2">
                    <div class="col-md-6">
                        <label for="sectionName_${containerId}_${i}" class="form-label">Section ${i + 1} Name</label>
                        <input type="text" class="form-control" id="sectionName_${containerId}_${i}" name="sections[${containerId}][${i}][name]" placeholder="Enter section name">
                    </div>
                    <div class="col-md-6">
                        <label for="duration_${containerId}_${i}" class="form-label">Duration</label>
                        <input type="number" class="form-control" id="duration_${containerId}_${i}" name="sections[${containerId}][${i}][duration]" placeholder="Minutes">
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', sectionHTML);
        }
    }

    function removeElement(elementId) {
        document.getElementById(elementId).remove();
    }

    // Add quarterly package functionality
    document.getElementById('addQuarterly').addEventListener('click', function () {
        const wrapper = document.getElementById('quarterlyPackageWrapper');
        const newItem = document.createElement('div');
        newItem.className = 'quarterly-package-item row g-3 mt-2';
        newItem.innerHTML = `
            <div class="col-md-3">
                <label>Name</label>
                <input type="text" class="form-control" name="quarterly_name[]" placeholder="Name">
            </div>
            <div class="col-md-3">
                <label>Start Date</label>
                <input type="date" class="form-control" name="quarterly_start_date[]">
            </div>
            <div class="col-md-3">
                <label>End Date</label>
                <input type="date" class="form-control" name="quarterly_end_date[]">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm removeQuarterly">X</button>
            </div>
        `;
        wrapper.appendChild(newItem);
    });

    document.addEventListener('click', function (e) {
        if (e.target && e.target.classList.contains('removeQuarterly')) {
            e.target.closest('.quarterly-package-item').remove();
        }
    });

    // Add session row for 1 to Many
    function addSessionRowM(packageType) {
        const container = document.getElementById(`sessionSchedule_1tomany_${packageType}`);
        const sessionRows = container.querySelectorAll('.session-row');
        const index = sessionRows.length;
        const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

        const row = document.createElement('div');
        row.classList.add('row', 'align-items-center', 'mb-2', 'session-row');

        row.innerHTML = `
            <div class="col-md-12 mb-2">
                <strong>${packageType}-Slots ${index + 1}</strong>
            </div>

            <div class="col-md-12 mb-2">
                <label class="form-label">Slot name</label>
                <input type="text" class="form-control" name="1tomany_${packageType}_sessions[${index}][slotname]" placeholder="Slot name">
            </div>

            <div class="col-md-12 mb-2">
                <label class="form-label">Day</label>
                ${days.map(day => `
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" name="1tomany_${packageType}_sessions[${index}][day][]" value="${day}"> ${day}
                    </div>
                `).join('')}
            </div>

            <div class="col-md-5 mb-2">
                <label class="form-label">Start Time</label>
                <input type="time" class="form-control" name="1tomany_${packageType}_sessions[${index}][start_time]" />
            </div>

            <div class="col-md-5 mb-2">
                <label class="form-label">End Time</label>
                <input type="time" class="form-control" name="1tomany_${packageType}_sessions[${index}][end_time]" />
            </div>

            <div class="col-md-2 mb-2">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeSessionRow(this)">x</button>
            </div>
        `;

        container.appendChild(row);
    }
</script>
@endpush
